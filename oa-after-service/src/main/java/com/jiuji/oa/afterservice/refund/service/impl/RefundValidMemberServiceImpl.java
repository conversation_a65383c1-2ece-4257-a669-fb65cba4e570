package com.jiuji.oa.afterservice.refund.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.oa.afterservice.bigpro.enums.SmsCodeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.smsconfig.SmsConfigService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.refund.bo.RefundValidBo;
import com.jiuji.oa.afterservice.refund.dao.RefundValidMemberMapper;
import com.jiuji.oa.afterservice.refund.vo.req.RefundValidVo;
import com.jiuji.oa.afterservice.smallpro.controller.SmallproController;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.sub.service.SubReturnCheckBeforeService;
import com.jiuji.oa.afterservice.sub.vo.req.SubReturnCheckBeforeReq;
import com.jiuji.oa.afterservice.sys.service.BaseValidMemberService;
import com.jiuji.oa.afterservice.sys.service.ValidMemberService;
import com.jiuji.oa.afterservice.sys.vo.req.BaseValid;
import com.jiuji.oa.afterservice.sys.vo.req.ValidMemberReq;
import com.jiuji.oa.afterservice.sys.vo.req.ValidReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.LambdaCaseWhen;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.xtenant.ShortXtenantEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/2 14:52
 */
@Service
@Slf4j
public class RefundValidMemberServiceImpl implements BaseValidMemberService<RefundValidVo> {
    @Resource
    private RefundValidMemberMapper refundValidMemberMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private SmsService smsService;
    @Override
    public R<Boolean> sendCode(ValidMemberReq<RefundValidVo> req) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        R<Boolean> e = commonCheckAndSet(req);
        if (!e.isSuccess()){
            return e;
        }
        RefundValidVo refundVo = req.getT();
        RefundValidBo refundValidBo = getReturnData(req.getOrderId(), refundVo);
        if(refundValidBo == null){
            return R.error("无效数据！");
        }
        switch (req.getValidTypeEnum()){
            case SMS_CODE:
                return sendSmsCode(req, oaUser, refundVo, refundValidBo);
            case AUTH_CODE:
                return sendAuthCode(req,oaUser,refundVo);

        }
        return R.error("该验证类型不支持发送验证信息");
    }

    /**
     * 生成授权验证信息
     * @param req
     * @param oaUser
     * @param refundVo
     * @return
     */
    private R<Boolean> sendAuthCode(ValidMemberReq<RefundValidVo> req, OaUserBO oaUser, RefundValidVo refundVo) {
        switch (refundVo.getTuihuanKindEnum()){
            case TPJ:
                // validType 3 为授权验证
                return SpringUtil.getBean(SmallproController.class).checkCodeMessage(refundVo.getShouhouId(),"授权",NumberConstant.TWO,NumberConstant.THREE);
            case SMALL_PRO_REFUND:
            case SMALL_PRO_HQTXH:
                return SpringUtil.getBean(SmallproController.class).checkCodeMessage(refundVo.getShouhouId(),"授权",NumberConstant.ONE,NumberConstant.THREE);
            default:
                break;
        }
        SubReturnCheckBeforeService checkBeforeService = SpringUtil.getBean(SubReturnCheckBeforeService.class);
        Validator validator = SpringUtil.getBean(Validator.class);
        Integer subType = 1;
        Integer operateType = 2;
        Long subId = req.getOrderId();
        switch (refundVo.getTuihuanKindEnum()){
            case TDJ:
                break;
            case TDJ_LP:
            case TK_LP:
                subType = 2;
                break;
            case TDJ_WXF:
                subType = 4;
                subId = Convert.toLong(refundVo.getShouhouId());
                break;
            default:
                operateType = 3;
        }
        Integer lastCheckStatus = checkBeforeService.getLastCheckStatus(subId, subType, operateType, oaUser.getUserId());
        if(lastCheckStatus != null){
            switch (lastCheckStatus){
                case 1:
                    return R.success("已经提交申请，请等待审核",Boolean.FALSE);
                case 2:
                    return R.success("审核通过",Boolean.TRUE);
                default:
                    break;
            }
        }
        boolean isAutoCheck = RankEnum.SMS_AUTH.hasAuthority(oaUser.getRank());
        SubReturnCheckBeforeReq srcReq = new SubReturnCheckBeforeReq().setAutoCheck(isAutoCheck).setComment(refundVo.getComment())
                .setSubId(subId).setSubType(subType).setOperateType(operateType).setAreaId(oaUser.getAreaId());
        if(isAutoCheck && StrUtil.isBlank(refundVo.getComment())){
            srcReq.setComment("申请授权");
        }
        Set<ConstraintViolation<SubReturnCheckBeforeReq>> violations = validator.validate(srcReq, Default.class);
        if (!violations.isEmpty()) {
            return R.error(violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining("/")));
        }
        R<Integer> checkR = checkBeforeService.submitCheckInfo(srcReq);
        if(checkR.isSuccess()){
            return DecideUtil.iif(isAutoCheck,R.success("审核通过",Boolean.TRUE),R.success("已经提交申请，请等待审核通过",Boolean.FALSE));
        }
        return R.error(checkR.getUserMsg());
    }

    /**
     * 发送短信验证信息
     * @param req
     * @param oaUser
     * @param refundVo
     * @param refundValidBo
     * @return
     */
    private R<Boolean> sendSmsCode(ValidMemberReq<RefundValidVo> req, OaUserBO oaUser, RefundValidVo refundVo, RefundValidBo refundValidBo) {
        switch (refundVo.getTuihuanKindEnum()){
            case TPJ:
                return SpringUtil.getBean(ValidMemberService.class).sendCode(new ValidMemberReq().setValidType(req.getValidType())
                        .setValidTypeEnum(req.getValidTypeEnum()).setBusinessType(BusinessTypeV1Enum.AFTER_SMALL_RETURN.getCode())
                        .setBusinessTypeEnum(BusinessTypeV1Enum.AFTER_SMALL_RETURN).setOrderId(Convert.toLong(refundVo.getShouhouId())));
            case SMALL_PRO_REFUND:
            case SMALL_PRO_HQTXH:
                return SpringUtil.getBean(ValidMemberService.class).sendCode(new ValidMemberReq().setValidType(req.getValidType())
                        .setValidTypeEnum(req.getValidTypeEnum()).setBusinessType(BusinessTypeV1Enum.AFTER_SMALL_CHANGE.getCode())
                        .setBusinessTypeEnum(BusinessTypeV1Enum.AFTER_SMALL_CHANGE).setOrderId(Convert.toLong(refundVo.getShouhouId())));
            default:
                break;
        }
        String codeKey = StrUtil.format(RedisKeys.TUIHUAN_VALID_CODE, req.getOrderId(), refundVo.getTuihuanKind());
        if (!stringRedisTemplate.hasKey(codeKey)){
            stringRedisTemplate.opsForValue().setIfAbsent(codeKey, StrUtil.format("{}",CommonUtils.getRandom4Code()), NumberConstant.FIVE, TimeUnit.MINUTES);
        }
        String numCode = stringRedisTemplate.opsForValue().get(codeKey);
        AreaInfo areaInfo = Optional.ofNullable(areaInfoClient.getAreaInfoById(oaUser.getAreaId())).map(R::getData).orElseThrow(() -> new CustomizeException("门店信息有误"));

        if(!Boolean.TRUE.equals(areaInfo.getIsSend())){
            return R.error("门店不支持发送短信");
        }
        log.warn("{}单号:{},售后单号:{},手机号:{},验证码:{}", req.getBusinessTypeEnum().getMessage(), req.getOrderId(),refundVo.getShouhouId(),refundValidBo.getMobile(), numCode);
        R<Boolean> sendResult;
        if(XtenantEnum.isJiujiXtenant()){
            sendResult = sendJiujiCodeContent(numCode, req, refundValidBo, oaUser);
        }else{
            sendResult = sendSaasCodeContent(numCode, req,areaInfo, refundValidBo, oaUser);
        }

        if (sendResult.isSuccess()) {
            return R.success("验证码发送成功", Boolean.TRUE).addAllBusinessLog(sendResult.businessLogs());
        } else {
            return new R(ResultCode.SERVER_ERROR, sendResult.getMsg(), sendResult.getUserMsg()).addAllBusinessLog(sendResult.businessLogs());
        }
    }

    private R<Boolean> sendSaasCodeContent(String numCode, ValidMemberReq<RefundValidVo> req, AreaInfo areaInfo, RefundValidBo refundValidBo, OaUserBO oaUser) {
        String smsFormat = SpringUtil.getBean(SmsConfigService.class).getSmsContentByCode(XtenantEnum.getXtenant(), SmsCodeEnum.TUI_KUAN_YAN_ZHENG_MA.getCode());
        String content = saasReplaceContent(numCode, req, areaInfo, smsFormat);
        Integer sendChannel;
        if (Objects.equals(refundValidBo.getBusinessType(), BusinessTypeEnum.NEW_ORDER.getCode())
                && CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant()) == ShortXtenantEnum.HENG_SHUI_TONG_HE.getCode()
                && refundValidMemberMapper.isContainOperatorData(req.getOrderId())) {
            sendChannel = 99;
        } else {
            sendChannel = smsService.getSmsChannelByTenant(oaUser.getAreaId(), ESmsChannelTypeEnum.YZMTD);
        }
        return smsService.sendSms(refundValidBo.getMobile(), content, DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(NumberConstant.TWO)),
                oaUser.getUserName(), sendChannel);
    }

    private String saasReplaceContent(String numCode, ValidMemberReq<RefundValidVo> req, AreaInfo areaInfo, String smsFormat) {
        return StrUtil.replace(smsFormat, PatternPool.get("(<printName>)?(<tHHmm>)?(<areaName>)?(<sub_id>)?(<code>)?"),
                matcher -> {
                    if (matcher.group(1) != null) {
                        return areaInfo.getPrintName();
                    }
                    if (matcher.group(2) != null) {
                        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm"));
                    }
                    if (matcher.group(3) != null) {
                        return areaInfo.getAreaName();
                    }
                    if (matcher.group(4) != null) {
                        return req.getOrderId() + "";
                    }
                    if (matcher.group(5) != null) {
                        return numCode;
                    }
                    return "";
                });
    }

    private R<Boolean> sendJiujiCodeContent(String numCode, ValidMemberReq<RefundValidVo> req, RefundValidBo refundValidBo, OaUserBO oaUser) {
        RefundValidVo refundVo = req.getT();
      //  String tips = DecideUtil.iif(Objects.equals(XtenantEnum.getXtenant(), XtenantEnum.Xtenant_JIUJI), "如有疑问可致电咨询：400-008-3939。", "");
        String tips = "";

        //售后id不为空 两个月内第二次无理由退款的顾客
        Integer userId = null;
        if (CommenUtil.isNotNullZero(refundVo.getShouhouId())
                && (userId = refundValidMemberMapper.getHuishouUserId(refundVo.getShouhouId())) != null
                && refundValidMemberMapper.count15DayNoReason(userId,LocalDate.now().plusMonths(-NumberConstant.TWO),refundVo.getShouhouId()) == 1){
            tips = StrUtil.format("温馨提示：系统检测到您近两月内已有2次无理由退款，2月内达到3次无理由退款，系统将限制良品购买权限（因质量问题不计）。为不影响您购机体验，请后续慎重选机{}。",tips);
        }
        //String msg = StrUtil.format("尊敬的客户，验证码{}，1分钟内有效，切勿泄露。您正在通过短信验证方式，对订单{}进行退款操作。{}", numCode, req.getOrderId(), tips);
        String msg = StrUtil.format("您正在进行[订单{}退款]操作，验证码{}。如非本人操作请忽略。{}", req.getOrderId(),numCode , tips);
        return smsService.sendSms(refundValidBo.getMobile(), msg, DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(NumberConstant.TWO)),
                oaUser.getUserName(), smsService.getSmsChannelByTenant(oaUser.getAreaId(), ESmsChannelTypeEnum.VERIFICATION_CODE));
    }

    @Override
    public R<Boolean> valid(ValidReq<RefundValidVo> req) {
        //公共验证
        R<Boolean> e = commonCheckAndSet(req);
        if (!e.isSuccess()){
            return e;
        }
        if(req.getValidCode() == null){
            //
            return R.error("验证码不能为空");
        }
        try {
            RefundValidVo refundValidVo = req.getT();
            switch (refundValidVo.getTuihuanKindEnum()){
                case TPJ:
                case SMALL_PRO_REFUND:
                case SMALL_PRO_HQTXH:
                    Smallpro smallpro = SpringUtil.getBean(SmallproService.class).getByIdSqlServer(refundValidVo.getShouhouId());
                    // 验证退款验证码
                    if (StrUtil.isBlank(smallpro.getCodeMsg())){
                        //调用小件退款验证接口进行验证
                        LambdaCaseWhen<TuihuanKindEnum, Integer> tuiKindCaseWhen = LambdaCaseWhen.lambdaCase(refundValidVo.getTuihuanKindEnum());
                        return SpringUtil.getBean(ValidMemberService.class).valid(new ValidReq().setValidCode(req.getValidCode())
                                .setValidType(req.getValidType())
                                .setBusinessType(tuiKindCaseWhen
                                        .when(TuihuanKindEnum.TPJ, () -> BusinessTypeV1Enum.AFTER_SMALL_RETURN.getCode())
                                        .when(TuihuanKindEnum.SMALL_PRO_REFUND, () -> BusinessTypeV1Enum.AFTER_SMALL_CHANGE.getCode())
                                        .when(TuihuanKindEnum.SMALL_PRO_HQTXH, () -> BusinessTypeV1Enum.AFTER_SMALL_CHANGE.getCode())
                                        .end()
                                ).setOrderId(Convert.toLong(refundValidVo.getShouhouId())));
                    }
                    return R.success(Boolean.TRUE);
                default:
                    break;
            }
            RefundValidBo refundValidBo;
            switch (req.getValidTypeEnum()){
                case SMS_CODE:
                    String codeKey = StrUtil.format(RedisKeys.TUIHUAN_VALID_CODE, req.getOrderId(), refundValidVo.getTuihuanKind());
                    String code = stringRedisTemplate.opsForValue().get(codeKey);
                    Assert.isFalse(StrUtil.isBlank(code), "请先发送验证码");
                    Assert.isTrue(Objects.equals(req.getValidCode(), code), "验证码错误！");
                    break;
                case PAY_PASSWORD:
                    refundValidBo = getReturnData(req.getOrderId(), refundValidVo);
                    Assert.isFalse(refundValidBo == null,"查无相关用户信息！");
                    SpringUtil.getBean(ValidMemberService.class).assertValidPayPwd(req.getValidCode(),refundValidBo.getUserid());
                    break;
                case MEMBER_CODE:
                    refundValidBo = getReturnData(req.getOrderId(), refundValidVo);
                    Assert.isFalse(refundValidBo == null,"查无相关用户信息！");
                    SpringUtil.getBean(ValidMemberService.class).assertValidMemberToken(req.getValidCode(),refundValidBo.getUserid());
                    break;
                case AUTH_CODE:
                    SubReturnCheckBeforeService checkBeforeService = SpringUtil.getBean(SubReturnCheckBeforeService.class);
                    Integer subType = 1;
                    Integer operateType = 2;
                    Long subId = req.getOrderId();
                    RefundValidVo refundVo = refundValidVo;
                    OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
                    switch (refundVo.getTuihuanKindEnum()){
                        case TDJ:
                            break;
                        case TDJ_LP:
                        case TK_LP:
                            subType = 2;
                            break;
                        case TDJ_WXF:
                            subType = 4;
                            subId = Convert.toLong(refundVo.getShouhouId());
                            break;
                        default:
                            operateType = 3;
                    }
                    Integer lastCheckStatus = checkBeforeService.getLastCheckStatus(subId, subType, operateType, oaUser.getUserId());
                    if(lastCheckStatus == null){
                        return R.error("没有授权申请记录");
                    }
                    switch (lastCheckStatus){
                        case 1:
                            return R.error("已经提交申请，请等待审核");
                        case 2:
                            return R.success("审核通过",Boolean.TRUE);
                        default:
                            break;
                    }
                    break;
                default:
                    return R.error("未实现该验证处理");
            }
        } catch (IllegalArgumentException iae) {
            return R.error(iae.getMessage());
        }
        return R.success(Boolean.TRUE);
    }

    @Override
    public boolean support(BusinessTypeV1Enum businessTypeEnum) {
        if (businessTypeEnum == null){
            return false;
        }
        switch (businessTypeEnum){
            case REFUND_MONEY:
                return true;
            default:
                return false;
        }
    }


    private R<Boolean> commonCheckAndSet(BaseValid<RefundValidVo> req) {
        try {
            //设置对象值
            Optional.ofNullable(req.getJson()).map(json -> json.toJavaObject(RefundValidVo.class)).ifPresent(req::setT);
            RefundValidVo refundValidVo = req.getT();
            Assert.isFalse(req.getJson() == null && refundValidVo == null,"退款验证参数错误");
            //设置枚举值
            if(CommenUtil.isNullOrZero(refundValidVo.getTuihuanKind()) && refundValidVo.getTuihuanKindEnum() != null){
                refundValidVo.setTuihuanKind(refundValidVo.getTuihuanKindEnum().getCode());
            }else if(refundValidVo.getTuihuanKindEnum() == null && CommenUtil.isNotNullZero(refundValidVo.getTuihuanKind())){
                refundValidVo.setTuihuanKindEnum(EnumUtil.getEnumByCode(TuihuanKindEnum.class, refundValidVo.getTuihuanKind()));
            }
            Assert.isFalse(refundValidVo.getTuihuanKind() == null, "退款类型不能为空");
            Assert.isFalse(refundValidVo.getTuihuanKindEnum() == null, "退款类型{}不支持", refundValidVo.getTuihuanKind());
            //良品单类型切换
            boolean isLpTk = TuihuanKindEnum.TK.equals(refundValidVo.getTuihuanKindEnum()) && ObjectUtil.defaultIfNull(refundValidVo.getShouhouId(), 0) > 0
                    && SpringUtil.getBean(ShouhouService.class).lambdaQuery().eq(Shouhou::getId, refundValidVo.getShouhouId()).eq(Shouhou::getIshuishou, NumberConstant.ONE).count() > 0;
            if(isLpTk){
                refundValidVo.setTuihuanKind(TuihuanKindEnum.TK_LP.getCode());
                refundValidVo.setTuihuanKindEnum(TuihuanKindEnum.TK_LP);
            }
        } catch (IllegalArgumentException e) {
            return R.error(e.getMessage());
        }
        return R.success(null);
    }

    private RefundValidBo getReturnData(Long subId,RefundValidVo refundVo){
        RefundValidBo refundValid;
        TuihuanKindEnum tuihuanKind = refundVo.getTuihuanKindEnum();
        switch (tuihuanKind){
            case TDJ_LP:
                refundValid = CommenUtil.autoQueryHist(() -> refundValidMemberMapper.getRefundValidByLp(subId), MTableInfoEnum.RECOVER_MARKET_INFO, subId);;
                break;
            case TDJ_WXF:
            case TWXF:
                refundValid = refundValidMemberMapper.getRefundValidByShouhou(subId);
                break;
            case TPJ:
            case SMALL_PRO_HQTXH:
                refundValid = CommenUtil.autoQueryHist(() -> refundValidMemberMapper.getRefundValidBySub(subId), MTableInfoEnum.SUB, subId);
                break;
            case TK:
            case HQTXH:
            case TK_LP:
                Shouhou subIdIshuishouMkcId = SpringUtil.getBean(ShouhouService.class).lambdaQuery()
                        .select(Shouhou::getSubId, Shouhou::getIshuishou, Shouhou::getMkcId).eq(Shouhou::getId, refundVo.getShouhouId()).one();
                if (subIdIshuishouMkcId == null){
                    return null;
                }
                if(ObjectUtil.defaultIfNull(subIdIshuishouMkcId.getIshuishou(),0)>0){
                    refundValid = CommenUtil.autoQueryHist(() -> refundValidMemberMapper.getRefundValidByLp(Convert.toLong(subIdIshuishouMkcId.getSubId())), MTableInfoEnum.RECOVER_MARKET_INFO, subId);
                }else if(ObjectUtil.defaultIfNull(subIdIshuishouMkcId.getSubId(),0) == 0){
                    refundValid = refundValidMemberMapper.getRefundValidByProductMkc(subIdIshuishouMkcId.getMkcId());
                }else{
                    refundValid = CommenUtil.autoQueryHist(() -> refundValidMemberMapper.getRefundValidBySub(Convert.toLong(subIdIshuishouMkcId.getSubId())), MTableInfoEnum.SUB, subId);
                }
                break;
            default:
                //查询新机单
                refundValid = refundValidMemberMapper.getRefundValidBySubUserMobile(subId);
                break;

        }
        if(refundValid != null){
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"refundValid: {}", JSON.toJSONString(refundValid));
        }
        return refundValid;
    }
}

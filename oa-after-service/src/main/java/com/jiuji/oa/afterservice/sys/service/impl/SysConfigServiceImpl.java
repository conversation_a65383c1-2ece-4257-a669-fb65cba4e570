package com.jiuji.oa.afterservice.sys.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.enums.ServiceTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.service.NumberCardService;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.UrlConstants;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.ValidatorUtil;
import com.jiuji.oa.afterservice.recover.enums.CompareOperatorTypeEnum;
import com.jiuji.oa.afterservice.recover.service.IRecoverPriceReviewConfigService;
import com.jiuji.oa.afterservice.recover.vo.req.PriceReviewConfigParentVO;
import com.jiuji.oa.afterservice.recover.vo.req.PriceReviewConfigVO;
import com.jiuji.oa.afterservice.shouhou.vo.res.ServiceIntroduce;
import com.jiuji.oa.afterservice.sub.service.SubSourceService;
import com.jiuji.oa.afterservice.sys.bo.OrderPaymentCompletesBO;
import com.jiuji.oa.afterservice.sys.dao.SysConfigMapper;
import com.jiuji.oa.afterservice.sys.enums.AfterExamineEnum;
import com.jiuji.oa.afterservice.sys.enums.ConfigEnum;
import com.jiuji.oa.afterservice.sys.enums.ConfigTitleEnum;
import com.jiuji.oa.afterservice.sys.enums.OperationTypeEnum;
import com.jiuji.oa.afterservice.sys.po.SysConfig;
import com.jiuji.oa.afterservice.sys.po.SysconfigLog;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.afterservice.sys.service.SysconfigLogService;
import com.jiuji.oa.afterservice.sys.vo.res.AfterExamineConfigVO;
import com.jiuji.oa.afterservice.sys.vo.res.BaseConfigVO;
import com.jiuji.oa.afterservice.sys.vo.res.ConfigEnumVO;
import com.jiuji.oa.afterservice.sys.vo.res.SysConfigEnumVO;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-15
 */
@Service
@Slf4j
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {
    @Autowired
    private ProductinfoService productinfoService;
    @Resource
    private SysconfigLogService sysconfigLogService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private IRecoverPriceReviewConfigService recoverPriceReviewConfigService;

    @Resource
    @Lazy
    private SysConfigService sysConfigService;
    @Resource
    private NumberCardService numberCardService;
    @Resource
    private SubSourceService subSourceService;


    private static final String NUMBER_OF_TOUCHPOINTS_NAME = "团购订单触发数量";

    /**
     * 实体类转换
     *
     * @param enumClass enumClass
     * @return
     */
    public static List<SysConfigEnumVO> toEnumVOList(Class<ConfigEnum> enumClass) {
        List<SysConfigEnumVO> enums = new ArrayList<>();
        ConfigEnum[] var2 = enumClass.getEnumConstants();
        for (ConfigEnum temp : var2) {
            SysConfigEnumVO vo = new SysConfigEnumVO();
            vo.setLabel(temp.getMessage());
            vo.setValue(temp.getCode());
            vo.setSettingsFlag(temp.getSettingsFlag());
            vo.setSwitchFlag(temp.getSwitchFlag());
            enums.add(vo);
        }
        return enums;
    }

    @Override
    public List<SysConfig> listAll() {
        return list();
    }

    @Override
    public String getWebNameByXtenant(Integer xtenant) {


        List<SysConfig> listAll = ((SysConfigService) AopContext.currentProxy()).listAll();
        SysConfig sysConfig = listAll.stream()
                .filter(t -> Objects.equals(t.getXtenant(), xtenant)).filter(t -> Objects.equals(t.getCode(), SysConfigConstant.PRINT_NAME))
                .findFirst().orElse(null);
        if (sysConfig == null) {
            return "九机";
        }
        return sysConfig.getValue();
    }

    @Override
    public List<Integer> getShouhouServicesPpriceids(Integer xtenant, Integer authId, Integer areaId) {

        List<Integer> ppidList = new LinkedList<>();
        List<SysConfig> listAll = ((SysConfigService) AopContext.currentProxy()).listAll();
        SysConfig sysConfig = listAll.stream()
                .filter(t -> t.getXtenant().equals(xtenant))
                .filter(t -> Objects.equals(t.getCode(), SysConfigConstant.SHOU_HOU_SERVICE_PPID) && t.getXtenant().equals(xtenant))
                .filter(t -> Objects.equals(t.getAuthId(), 0))
                .filter(t -> StringUtils.isEmpty(t.getAreaids()) || t.getAreaids().contains(areaId.toString()))
                .findFirst().orElse(null);

        if (sysConfig == null) {
            return ppidList;
        }
        ppidList = Arrays.stream(sysConfig.getValue().split(",")).map(t -> Integer.valueOf(t)).collect(Collectors.toList());
        return ppidList;
    }

    @Override
    public List<Integer> getShouhouServiceTypeList(Integer xtenant) {
        return null;
    }

    @Override
    public String getValueByCodeAndXtenant(Integer code, Integer xtenant) {
        List<SysConfig> listAll = ((SysConfigService) AopContext.currentProxy()).listAll();

        SysConfig sysConfig = listAll.stream()
                .filter(t -> Objects.equals(t.getXtenant(), xtenant)).filter(t -> Objects.equals(t.getCode(), code))
                .findFirst().orElse(null);
        if (sysConfig == null) {
            return "";
        }
        return sysConfig.getValue();
    }

    @Override
    public String getValueByCode(Integer code) {
        List<SysConfig> listAll = ((SysConfigService) AopContext.currentProxy()).listAll();
        SysConfig sysConfig = listAll.stream()
                .filter(t -> Objects.equals(t.getCode(), code))
                .findFirst().orElse(null);
        if (sysConfig == null) {
            return "";
        }
        if (log.isDebugEnabled()) {
            log.debug("系统配置: {}", JSON.toJSONString(sysConfig, SerializerFeature.PrettyFormat));
        }
        return sysConfig.getValue();
    }

    @Override
    public String getValueByCodeNoCache(Integer code) {
        return lambdaQuery().eq(SysConfig::getCode, code).orderByDesc(SysConfig::getId).select(SysConfig::getValue).list()
                .stream().findFirst().map(SysConfig::getValue).orElse(null);
    }

    @Override
    public String getDevopsExceptionUserId(Integer xtenant) {
        List<SysConfig> listAll = ((SysConfigService) AopContext.currentProxy()).listAll();
        SysConfig sysConfig = listAll.stream().filter(t -> Objects.equals(t.getCode(), SysConfigConstant.EXCEPTION_PUSH_USER_ID) && t.getXtenant().equals(xtenant)).findAny().orElse(null);
        if (sysConfig != null) {
            return sysConfig.getValue();
        }
        return null;
    }

    @Override
    public String getServiceInfoUrl(Integer xtenant, String imei) {
        String url = UrlConstants.REPAIR_SERVICE_URL;
        if (!xtenant.equals(0)) {
            url = url.replaceAll(":988", "");
        }
        return String.format(url, this.getValueByCodeAndXtenant(SysConfigConstant.OA_WCF_HOST, xtenant), imei, xtenant);
    }

    @Override
    public Integer getWxIdByAreaId(Integer areaId) {
        String switchValue = this.getValueByCode(SysConfigConstant.MULTI_WXID_SWITCH);
        log.debug("是否启用多微信:{}", switchValue);
        if (StringUtils.isNotEmpty(switchValue) && Objects.equals(switchValue, "1")) {
            //启用多微信
            return baseMapper.getWxIdByAreaId(areaId);
        }
        String wxIdStr = this.getValueByCode(SysConfigConstant.WX_ID);
        if (StringUtils.isEmpty(wxIdStr)) {
            wxIdStr = "1";
            log.debug("微信id没有配置取默认值:{}", wxIdStr);
        }
        return Integer.valueOf(wxIdStr);
    }

    @Override
    public ServiceIntroduce getServiceIntroduce(Integer serviceType) {

        ServiceIntroduce introduce = new ServiceIntroduce();
        ServiceTypeEnum serviceEnum = EnumUtil.getEnumByCode(ServiceTypeEnum.class, serviceType);
        if (serviceEnum != null) {
            introduce.setDescription(serviceEnum.getMessage() + "服务介绍");
            introduce.setServiceName(serviceEnum.getMessage());
        }
        List<SysConfig> listAll = ((SysConfigService) AopContext.currentProxy()).listAll();
        SysConfig sysConfig = listAll.stream()
                .filter(t -> serviceEnum != null && Objects.equals(t.getCode(), serviceEnum.getServicePpid()))
                .findFirst().orElse(null);
        if (sysConfig == null) {
            return introduce;
        }

        introduce.setUrl(sysConfig.getValue());
        List<Productinfo> list = productinfoService.list(new LambdaQueryWrapper<Productinfo>().eq(Productinfo::getPpriceid, serviceEnum.getServicePpid()));
        if (CollectionUtils.isNotEmpty(list)) {
            introduce.setProductColor(list.get(0).getProductColor());
        }

        return introduce;
    }

    @Override
    public SysConfig getByCodeAndXtenant(Integer code, Long xtenant) {
        return this.baseMapper.getByCodeAndXtenant(code, xtenant);
    }

    @Override
    public List<String> getSysConfigOptions(Integer code) {
        List<String> options = new LinkedList<>();
        String value = this.getValueByCode(code);
        if (StringUtils.isNotEmpty(value)) {
            options = Arrays.asList(value.split(","));
        }
        return options;
    }

    /**
     * 获取系统配置信息
     *
     * @return ConfigEnumVO 系统配置信息
     */
    @Override
    public List<ConfigEnumVO> listSysConfig() {
        //根据枚举类的code值查找相对应的配置信息  注意：枚举里面有一个是否全局配置 如果是则更据code查询全部，否则根据Xtenant和code来查询一条值
        //获取所有是全局配置的code值
        List<Integer> settingsCollect = toEnumVOList(ConfigEnum.class).stream().filter(s -> Boolean.TRUE.equals(s.getSettingsFlag())).map(SysConfigEnumVO::getValue).collect(Collectors.toList());
        //获取所有非全局配置的code值
        List<Integer> notSettingsCollect = toEnumVOList(ConfigEnum.class).stream().filter(s -> Boolean.FALSE.equals(s.getSettingsFlag())).map(SysConfigEnumVO::getValue).collect(Collectors.toList());
        if (XtenantEnum.isJiujiXtenant()) {
            settingsCollect.remove(ConfigEnum.NUMBER_OF_TOUCHPOINTS.getCode());
            settingsCollect.remove(ConfigEnum.REPORT_EXCLUDES_CROSS_AUTHORIZED_TRANSFER_ORDERS.getCode());
            notSettingsCollect.remove(ConfigEnum.AFTER_SALES_OR_PRE_SALES_9.getCode());
            notSettingsCollect.remove(ConfigEnum.NATIONAL_USER_ADDRESS.getCode());
//            notSettingsCollect.remove(ConfigEnum.RECYCLE_OPT_DIFF_SYNC.getCode());
        }
        if (XtenantEnum.isSaasXtenant()) {
            settingsCollect.remove(ConfigEnum.RECOVERY_RETURN_CID.getCode());
            settingsCollect.remove(ConfigEnum.RECOVER_LOCK_REVIEW.getCode());
            settingsCollect.remove(ConfigEnum.RECOVER_SHOP_CHECK_REVIEW.getCode());
        }
        List<SysConfig> list = new ArrayList<>();
        //全局配置的  更据code查询id最小的一条数据
        List<SysConfig> settingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settingsCollect)) {
            List<SysConfig> list1 = list(new LambdaQueryWrapper<SysConfig>().in(SysConfig::getCode, settingsCollect)
                    .orderByAsc(SysConfig::getRank));
            if (XtenantEnum.isSaasXtenant()) {
                SysConfig orderPaymentCompletesPpidConfig = list1.stream().filter(v -> ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getCode().equals(v.getCode())).findFirst().orElse(null);
                SysConfig orderPaymentCompletesPrintConfig = list1.stream().filter(v -> ConfigEnum.ORDER_PAYMENT_COMPLETES_PRINT.getCode().equals(v.getCode())).findFirst().orElse(null);
                OrderPaymentCompletesBO orderPaymentCompletes = new OrderPaymentCompletesBO();
                orderPaymentCompletes.setOrderPaymentCompletesPpid(Optional.ofNullable(orderPaymentCompletesPpidConfig).map(SysConfig::getValue).orElse(""));
                orderPaymentCompletes.setOrderPaymentCompletesPrint(Optional.ofNullable(orderPaymentCompletesPrintConfig).map(SysConfig::getValue).orElse(""));
                if (orderPaymentCompletesPpidConfig != null) {
                    orderPaymentCompletesPpidConfig.setName(ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getMessage());
                    orderPaymentCompletesPpidConfig.setValue(JSONUtil.toJsonStr(orderPaymentCompletes));
                    settingList.add(orderPaymentCompletesPpidConfig);
                } else {
                    orderPaymentCompletesPpidConfig = new SysConfig();
                    ConfigEnum enumByCode = ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID;
                    orderPaymentCompletesPpidConfig.setCode(enumByCode.getCode())
                            .setSettingsFlag(enumByCode.getSettingsFlag())
                            .setSwitchFlag(enumByCode.getSwitchFlag())
                            .setDsc(enumByCode.getDsc())
                            .setTitleType(enumByCode.getConfigTitleEnum().getCode())
                            .setCheckSize(enumByCode.getCheckSize())
                            .setIsdel(Boolean.FALSE).setXtenant(XtenantEnum.getXtenant())
                            .setId(NumberConstant.ZERO);
                    orderPaymentCompletesPpidConfig.setName(ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getMessage());
                    orderPaymentCompletesPpidConfig.setValue(JSONUtil.toJsonStr(orderPaymentCompletes));
                    settingList.add(orderPaymentCompletesPpidConfig);
                }
            }
            list1 = list1.stream().filter(v -> !ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getCode().equals(v.getCode()) && !ConfigEnum.ORDER_PAYMENT_COMPLETES_PRINT.getCode().equals(v.getCode())).collect(Collectors.toList());
            //根据code分组，过滤出id最小的值
            Map<Integer, List<SysConfig>> collect1 = list1.stream().collect(Collectors.groupingBy(SysConfig::getCode));
            collect1.forEach((key, value) -> {
                if (!Objects.equals(key, ConfigEnum.RECOVER_PRICE_REVIEW.getCode())) {
                    Optional<SysConfig> min = value.stream().filter(Objects::nonNull).min(Comparator.comparing(SysConfig::getId));
                    settingList.add(min.orElse(null));
                }else {
                    SysConfig sysConfig = value.stream().filter(x -> Objects.equals(x.getXtenant(), abstractCurrentRequestComponent.getCurrentStaffId().getXTenant())).findFirst().orElse(null);
                    settingList.add(sysConfig);
                }
            });
        }
        //非全局配置的  根据Xtenant和code来查询一条值
        List<SysConfig> notSettingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(notSettingsCollect)) {
            List<SysConfig> list2 = list(new LambdaQueryWrapper<SysConfig>().in(SysConfig::getCode, notSettingsCollect)
                    .eq(SysConfig::getXtenant, abstractCurrentRequestComponent.getCurrentStaffId().getXTenant())
                    .orderByAsc(SysConfig::getRank));
            //根据code分组，过滤出id最小的值
            Map<Integer, List<SysConfig>> collect2 = list2.stream().collect(Collectors.groupingBy(SysConfig::getCode));
            collect2.forEach((key, value) -> {
                Optional<SysConfig> min = value.stream().filter(Objects::nonNull).min(Comparator.comparing(SysConfig::getId));
                notSettingList.add(min.orElse(null));
            });
        }
        //设置一些属性值
        if (CollectionUtils.isNotEmpty(settingList)) {
            settingList.forEach(s -> {
                s.setSwitchFlag(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getSwitchFlag());
                s.setDsc(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getDsc());
                s.setSettingsFlag(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getSettingsFlag());
                s.setTitleType(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getConfigTitleEnum().getCode());
                s.setCheckSize(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getCheckSize());
                if (Objects.equals(s.getCode(), ConfigEnum.RECOVER_PRICE_REVIEW.getCode())) {
                    s.setXtenant(abstractCurrentRequestComponent.getCurrentStaffId().getXTenant());
                    String valueByCodeAndXtenant = s.getValue();
                    PriceReviewConfigVO configList = recoverPriceReviewConfigService.getConfigList(Convert.toInt(abstractCurrentRequestComponent.getCurrentStaffId().getXTenant()));
                    if ("true".equals(valueByCodeAndXtenant)) {
                        configList.setNotReviewFlag("true");
                    } else if ("false".equals(valueByCodeAndXtenant)) {
                        configList.setNotReviewFlag("false");
                    } else if (StringUtils.isNotEmpty(valueByCodeAndXtenant)) {
                        try {
                            PriceReviewConfigParentVO bean = JSONUtil.toBean(valueByCodeAndXtenant, PriceReviewConfigParentVO.class);
                            if (Objects.nonNull(bean) && "true".equals(bean.getNotReviewFlag())) {
                                configList.setNotReviewFlag("true");
                            }
                            if (Objects.nonNull(bean) && Objects.nonNull(bean.getOverTime())) {
                                configList.setOverTime(bean.getOverTime());
                            }
                            if (Objects.nonNull(bean) && Objects.nonNull(bean.getAddPriceLimitType())) {
                                configList.setAddPriceLimitType(bean.getAddPriceLimitType());
                            }else {
                                configList.setAddPriceLimitType( CompareOperatorTypeEnum.LE.getCode());
                            }
                            if (Objects.nonNull(bean) && Objects.nonNull(bean.getEvaPriceLimitType())) {
                                configList.setEvaPriceLimitType(bean.getEvaPriceLimitType());
                            }else {
                                configList.setEvaPriceLimitType (CompareOperatorTypeEnum.GE.getCode());
                            }
                            if (Objects.nonNull(bean.getEvaPriceLimit())) {
                                configList.setEvaPriceLimit(bean.getEvaPriceLimit());
                            }else if(Objects.equals("true",configList.getNotReviewFlag())){
                                configList.setEvaPriceLimit(BigDecimal.valueOf(9));
                            }else {
                                configList.setEvaPriceLimit(BigDecimal.valueOf(0));
                            }
                            if (Objects.nonNull(bean.getAddPriceLimit())) {
                                configList.setAddPriceLimit(bean.getAddPriceLimit());
                            }else if(Objects.equals("true",configList.getNotReviewFlag())){
                                configList.setAddPriceLimit(BigDecimal.valueOf(9));
                            }else {
                                configList.setAddPriceLimit(BigDecimal.valueOf(0));
                            }
                        } catch (Exception e) {

                        }
                    }
                    configList.setCode(s.getCode());
                    configList.setConfigId(s.getId());
                    s.setValue(JSONUtil.toJsonStr(configList));
                }
                if (Objects.equals(ConfigEnum.SUB_SOURCE.getCode(),s.getCode())) {
                    s.setXtenant(XtenantEnum.getXtenant());
                    List<Tree<Integer>> subSourceTree = subSourceService.getSubSourceTree();
                    s.setValue(JSONUtil.toJsonStr(subSourceTree));
                }
            });
            list.addAll(settingList);
        }
        if (CollectionUtils.isNotEmpty(notSettingList)) {
            notSettingList.forEach(s -> {
                s.setSwitchFlag(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getSwitchFlag());
                s.setDsc(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getDsc());
                s.setSettingsFlag(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getSettingsFlag());
                s.setTitleType(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getConfigTitleEnum().getCode());
                s.setCheckSize(Objects.requireNonNull(ConfigEnum.getEnumByCode(s.getCode())).getCheckSize());
            });
            list.addAll(notSettingList);
        }
        //对于没有code值的数据进行构建参数处理
        List<Integer> collect = toEnumVOList(ConfigEnum.class).stream().map(SysConfigEnumVO::getValue).collect(Collectors.toList());
        List<Integer> collect1 = list.stream().map(SysConfig::getCode).collect(Collectors.toList());
        Collection<Integer> disjunction = CollUtil.disjunction(collect, collect1);
        //九机剔除
        if(XtenantEnum.isJiujiXtenant() && CollectionUtils.isNotEmpty(disjunction)){
            disjunction.remove(ConfigEnum.NUMBER_OF_TOUCHPOINTS.getCode());
            disjunction.remove(ConfigEnum.REPORT_EXCLUDES_CROSS_AUTHORIZED_TRANSFER_ORDERS.getCode());
        }
        if (XtenantEnum.isSaasXtenant() && CollectionUtils.isNotEmpty(disjunction)) {
            disjunction.remove(ConfigEnum.RECOVERY_RETURN_CID.getCode());
            disjunction.remove(ConfigEnum.RECOVER_LOCK_REVIEW.getCode());
            disjunction.remove(ConfigEnum.RECOVER_SHOP_CHECK_REVIEW.getCode());
        }
        for (Integer integer : disjunction) {
            if (ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getCode().equals(integer) || ConfigEnum.ORDER_PAYMENT_COMPLETES_PRINT.getCode().equals(integer)) {
                continue;
            }
            ConfigEnum enumByCode = ConfigEnum.getEnumByCode(integer);
            SysConfig sysConfig = new SysConfig();
            if (enumByCode != null) {
                sysConfig.setCode(enumByCode.getCode()).setSettingsFlag(enumByCode.getSettingsFlag()).setSwitchFlag(enumByCode.getSwitchFlag())
                        .setName(enumByCode.getMessage()).setDsc(enumByCode.getDsc()).setTitleType(enumByCode.getConfigTitleEnum().getCode()).setCheckSize(enumByCode.getCheckSize())
                        .setIsdel(Boolean.FALSE).setXtenant(NumberConstant.ZERO).setId(NumberConstant.ZERO).setValue(String.valueOf(NumberConstant.ZERO));
                //特殊处理code是121的  默认是开启状态
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.AFTER_SALES_OR_PRE_SALES_2.getCode())) {
                    sysConfig.setValue(String.valueOf(NumberConstant.ONE));
                }
                //特殊处理 团购订单定义配置
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.NUMBER_OF_TOUCHPOINTS.getCode())) {
                    sysConfig.setName(NUMBER_OF_TOUCHPOINTS_NAME);
                }
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.AFTER_SALES_OR_PRE_SALES_9.getCode())) {
                    sysConfig.setValue(String.valueOf(NumberConstant.ONE));
                }
                // 是否同步拍机堂复检回收差异 默认 开启
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.RECYCLE_OPT_DIFF_SYNC.getCode())) {
                    sysConfig.setValue(String.valueOf(NumberConstant.ONE));
                }
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.RECOVER_LOCK_REVIEW.getCode())) {
                    sysConfig.setValue(String.valueOf(NumberConstant.ONE));
                }
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.RECOVER_SHOP_CHECK_REVIEW.getCode())) {
                    sysConfig.setValue(String.valueOf(NumberConstant.ONE));
                }
                // 低价值机标准配置 默认 21
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.RECYCLE_LOW_PRICE_MACHINE.getCode())) {
                    sysConfig.setValue(String.valueOf(21));
                }
                // 回收加单免锁单账号配置 默认 ""
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.RECYCLE_UNLOCK_CONFIG.getCode())) {
                    sysConfig.setValue("");
                }
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.RECOVER_PRICE_REVIEW.getCode())) {
                    sysConfig.setValue("");
                }
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.RECYCLE_FOLLOW_NEW_CULL_SUB_CONFIG.getCode())) {
                    sysConfig.setValue("{\"isCullGroup\":false,\"cullSubType\":[]}");
                }
                if (Objects.equals(sysConfig.getCode(), ConfigEnum.SUB_SOURCE.getCode())) {
                    sysConfig.setXtenant(XtenantEnum.getXtenant());
                    List<Tree<Integer>> subSourceTree = subSourceService.getSubSourceTree();
                    sysConfig.setValue(JSONUtil.toJsonStr(subSourceTree));
                }

                list.add(sysConfig);
            }
        }
        //排序
        Map<Integer, List<SysConfig>> sysConfigInfo = list.stream()
                .sorted(Comparator.comparing(SysConfig::getId))
                .collect(Collectors.groupingBy(SysConfig::getCode));
        return getLogistics(sysConfigInfo);
    }

    @Override
    public List<SysconfigLog> getSysconfigLog(Integer code) {
        if (ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getCode().equals(code)) {
            List<Integer> codeList = Arrays.asList(ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getCode(), ConfigEnum.ORDER_PAYMENT_COMPLETES_PRINT.getCode());
            return sysconfigLogService.list(new LambdaQueryWrapper<SysconfigLog>().in(SysconfigLog::getCode, codeList).orderByDesc(SysconfigLog::getId));
        }
        return sysconfigLogService.list(new LambdaQueryWrapper<SysconfigLog>().eq(SysconfigLog::getCode, code).orderByDesc(SysconfigLog::getId));
    }

    /**
     * 数据据校验
     * @param list
     */
    private void checkSysConfig(List<SysConfig> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for (SysConfig sysConfig : list){
            // 订单团单检测触达发数量 的时候校验 必须为大于0的数字
            if(ConfigEnum.NUMBER_OF_TOUCHPOINTS.getCode().equals( sysConfig.getCode())){
                String value = Optional.ofNullable(sysConfig.getValue()).orElse("0");
                if(!(NumberUtil.isInteger(value) && Integer.parseInt(value)>NumberConstant.ZERO)){
                    throw new CustomizeException("请输入最小值为1最大值为"+Integer.MAX_VALUE+"的正整数范围");
                }
            }
            // 回收，返还的旧件分类配置 必须是都好拼接的cid
            if(ConfigEnum.RECOVERY_RETURN_CID.getCode().equals( sysConfig.getCode())){
                String value = Optional.ofNullable(sysConfig.getValue()).orElse("0");
                if (!value.trim().matches("^\\d+(,\\d+)*$")) {
                    throw new CustomizeException("请输入逗号分隔的cid");
                }
            }
        }
    }

    /**
     * 更新配置 逻辑注意点：当更新时查询更新内容是否存在，如果不存在则插入数据库
     *
     * @param list 配置信息
     * @return boolean 返回值
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateSysConfig(List<SysConfig> list, OaUserBO user) {
        Integer code = list.get(0).getCode();
        Assert.notNull(code, "code不能为空");
        //数据校验
        checkSysConfig(list);
        //获取枚举类 判断是否是全局配置
        List<Integer> settingsCollect = toEnumVOList(ConfigEnum.class).stream().filter(s -> Boolean.TRUE.equals(s.getSettingsFlag())).map(SysConfigEnumVO::getValue).collect(Collectors.toList());
        List<SysConfig> sysConfigList;
        if (settingsCollect.contains(code)) {
            sysConfigList = this.lambdaQuery().eq(SysConfig::getCode, code).list();
        } else {
            sysConfigList = this.lambdaQuery().eq(SysConfig::getCode, code).eq(SysConfig::getXtenant, XtenantEnum.getXtenant()).list();
        }
        //当配置不存在时,进行添加该配置处理
        if (CollectionUtils.isEmpty(sysConfigList) && CommenUtil.isNullOrZero(list.get(0).getId())) {
            //记录日志
            List<SysConfig> addSysConfigList = sysconfigLogService.addSysConfig(null, list, user);
            if (CollectionUtils.isNotEmpty(addSysConfigList)) {
                addSysConfigList.forEach(add -> add.setXtenant(user.getXTenant()).setDsc(add.getName()));
                String s = checkShoppingBag(addSysConfigList);
                if (StrUtil.isNotEmpty(s)) {
                    return "请检查PPID是否是购物袋类型：" + s;
                }
                this.baseMapper.sqlServerBatchInsert(addSysConfigList);
            }
        }
        //更新
        List<SysConfig> editSysConfigList = sysconfigLogService.editSysConfig(sysConfigList, list, user);
        if (CollectionUtils.isNotEmpty(editSysConfigList)) {
            for (int i = 0; i < editSysConfigList.size(); i++) {
                SysConfig add = editSysConfigList.get(i);
                if (ConfigEnum.PROMO_CODE_7.getCode().equals(add.getCode()) && StringUtils.isNotEmpty(add.getValue())) {
                    //判断优惠券是否有效(luojianguo)
                    String[] cardIds = add.getValue().trim().split(",");
                    List<String> cardIdList = Arrays.asList(cardIds);
                    List<String> yxCardIdList = numberCardService.getYxNumberCardByCardIdList(cardIdList);
                    //求差集
                    List<String> cj = cardIdList.stream().filter(e -> !yxCardIdList.contains(e)).collect(Collectors.toList());
                    if (cj.size() > 0) {
                        return "请检查下列优惠券是否存在：" + cj;
                    }
                }
            }

            editSysConfigList.forEach(add -> add.setDsc(add.getName()));
            String s = checkShoppingBag(editSysConfigList);
            if (StrUtil.isNotEmpty(s)) {
                return "请检查PPID是否是购物袋类型：" + s;
            }
            this.updateBatchById(editSysConfigList);
        }
        //删除
        List<Integer> removeSysConfigList = sysconfigLogService.removeSysConfig(sysConfigList, list, user);
        if (CollectionUtils.isNotEmpty(removeSysConfigList)) {
            this.removeByIds(removeSysConfigList);
        }
        return "true";
    }

    /**
     * 更新订单支付完成自动交易配置
     *
     * @param list 配置信息
     * @return boolean 返回值
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateOrderPaymentCompletesConfig(List<SysConfig> list, OaUserBO user) {
        SysConfig sysConfig = list.stream().findFirst().orElse(new SysConfig());
        OrderPaymentCompletesBO orderPaymentCompletes = JSONUtil.toBean(sysConfig.getValue(), OrderPaymentCompletesBO.class);
        ValidatorUtil.validateEntity(orderPaymentCompletes);
        List<SysConfig> ppidList = sysConfigService.lambdaQuery().eq(SysConfig::getCode, ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getCode()).list();
        List<SysConfig> printList = sysConfigService.lambdaQuery().eq(SysConfig::getCode, ConfigEnum.ORDER_PAYMENT_COMPLETES_PRINT.getCode()).list();
        if (CollectionUtils.isEmpty(ppidList)) {
            SysConfig sysCon = new SysConfig();
            sysCon.setValue("");
            sysCon.setCode(ConfigEnum.ORDER_PAYMENT_COMPLETES_PPID.getCode());
            sysCon.setName("订单支付完成自动交易ppid");
            sysCon.setDsc("订单支付完成自动交易ppid");
            ppidList = new ArrayList<>();
            ppidList.add(sysCon);
        }
        if (CollectionUtils.isEmpty(printList)) {
            SysConfig sysCon = new SysConfig();
            sysCon.setValue("");
            sysCon.setCode(ConfigEnum.ORDER_PAYMENT_COMPLETES_PRINT.getCode());
            sysCon.setName("订单支付完成自动交易打印客户端编号");
            sysCon.setDsc("订单支付完成自动交易打印客户端编号");
            printList = new ArrayList<>();
            printList.add(sysCon);
        }
        List<SysConfig> paymentCompletesPpid = ppidList.stream().map(v -> {
            SysConfig sysCon = BeanUtil.toBean(v, SysConfig.class);
            sysCon.setValue(orderPaymentCompletes.getOrderPaymentCompletesPpid());
            return sysCon;
        }).collect(Collectors.toList());
        List<SysConfig> paymentCompletesPrint = printList.stream().map(v -> {
            SysConfig sysCon = BeanUtil.toBean(v, SysConfig.class);
            sysCon.setValue(orderPaymentCompletes.getOrderPaymentCompletesPrint());
            return sysCon;
        }).collect(Collectors.toList());
        String msg = "";
        msg = sysConfigService.updateSysConfig(paymentCompletesPpid, user);
        msg = sysConfigService.updateSysConfig(paymentCompletesPrint, user);
        return msg;
    }

    /**
     * 更新订单来源配置
     *
     * @param list
     * @return
     */
    @Override
    public R<Boolean> updateSubSourceConfig(List<SysConfig> list) {
        return R.success(subSourceService.editSubSourceTree(list));
    }


    /**
     * 更新售后审核配置
     *
     * @param afterExamineConfigVOS
     * @param currentStaffId
     * @return
     */
    @Override
    public R updateAfterExamineConfig(Collection<AfterExamineConfigVO> afterExamineConfigVOS, OaUserBO currentStaffId) {
        //首先获取已有配置
        List<AfterExamineConfigVO> oldAfterExamineConfigVOS = getAfterExamineList();
        Map<Integer, AfterExamineConfigVO> oldafterExamineConfigVOMap = oldAfterExamineConfigVOS.stream().collect(Collectors.toMap(AfterExamineConfigVO::getCode, Function.identity(), (key1, key2) -> key2));
        //修改的列表
        List<SysConfig> editSysConfigList = new ArrayList<>();
        //修改日志
        List<SysconfigLog> sysConfigLogList = new ArrayList<>(editSysConfigList.size());
        //便利前端传入的修改信息
        for (AfterExamineConfigVO afterExamineConfigVO : afterExamineConfigVOS) {
            //得到修改前的配置
            AfterExamineConfigVO oldAfterExamineConfigVO = oldafterExamineConfigVOMap.get(afterExamineConfigVO.getCode());
            //计算新、老的配置值
            int newValue = computeValue(afterExamineConfigVO.getFirExamineSwitch(), afterExamineConfigVO.getSecExamineSwitch());
            int oldValue = computeValue(oldAfterExamineConfigVO.getFirExamineSwitch(), oldAfterExamineConfigVO.getSecExamineSwitch());
            //如果没有修改则跳过
            if (newValue == oldValue) {
                continue;
            }
            //有修改则创建修改实体对象
            SysConfig editSysconfig = new SysConfig();
            editSysconfig.setId(afterExamineConfigVO.getId());
            editSysconfig.setName(afterExamineConfigVO.getName());
            editSysconfig.setCode(afterExamineConfigVO.getCode());
            editSysconfig.setValue(String.valueOf(newValue));
            editSysConfigList.add(editSysconfig);
            //创建修改日志
            SysconfigLog sysConfigLog = new SysconfigLog().setCode(afterExamineConfigVO.getCode())
                    .setOperationType(OperationTypeEnum.SYSCONFIG_UPDATE.getCode());
            sysConfigLog.setConfigId(afterExamineConfigVO.getId());
            String comment = "更新配置：" + afterExamineConfigVO.getName() + "，旧值:" + getConfigStringByValue(oldValue) + "; 新值: " + getConfigStringByValue(newValue);
            sysConfigLog.setComment(comment);
            sysConfigLog.setInUserId(currentStaffId.getUserId());
            sysConfigLog.setInUserName(currentStaffId.getUserName());
            sysConfigLogList.add(sysConfigLog);
        }
        //如果修改列表为空则返回
        if (CollectionUtils.isEmpty(editSysConfigList)) {
            return R.success("true");
        }
        //更新配置
        this.updateBatchById(editSysConfigList);
        //储存日志
        sysconfigLogService.sqlServerBatchInsert(sysConfigLogList);
        return R.success("true");
    }

    @Override
    public List<SysconfigLog> getAfterExamineConfigLog() {
        //通过售后审核枚举得到售后审核配置的code
        List<Integer> settingsCollect = Arrays.stream(AfterExamineEnum.values()).map(AfterExamineEnum::getCode).collect(Collectors.toList());
        //通过售后审核配置的code得到所有配置
        List<SysconfigLog> sysconfigLogs = sysconfigLogService.selectLogsByCodes(settingsCollect);
        return sysconfigLogs.stream().sorted(Comparator.comparing(SysconfigLog::getId).reversed()).collect(Collectors.toList());
    }

    @Override
    public AfterExamineConfigVO getAfterExamineByCode(Integer code) {
        List<SysConfig> sysConfigs = list(new LambdaQueryWrapper<SysConfig>().in(SysConfig::getCode, code)
                .orderByAsc(SysConfig::getRank));
        //过滤出id最小的值
        SysConfig sysConfig = sysConfigs.stream().filter(Objects::nonNull).min(Comparator.comparing(SysConfig::getId)).orElse(null);
        if (sysConfig == null) {
            return null;
        }
        return sysConfigtoAfterExamineConfigVO(sysConfig);
    }

    @Override
    public boolean isOutDepot(Integer areaId, Integer xtenant) {
        String value = getValueByCodeAndXtenant(SysConfigConstant.OUT_DEPOT_AREAIDS, xtenant);
        if (org.apache.commons.lang3.StringUtils.isBlank(value)
                || Objects.isNull(areaId)) {

            return false;
        }
        List<Integer> areaIds = CommonUtils.covertIdStr(value);
        return areaIds.stream()
                .anyMatch(item -> item.equals(areaId));
    }

    @Override
    public List<SysConfig> listIncludeDelInCode(Collection<Integer> codeList) {
        return baseMapper.listIncludeDelInCode(codeList);
    }

    /**
     * 校验ppid SysConfigConstant.ORDER_SHOPPING_BAG 的值是否是购物袋的ppid
     *
     * @param sysConfigList sysConfigList
     * @return
     */
    private String checkShoppingBag(List<SysConfig> sysConfigList) {
        List<Integer> integers = Optional.of(sysConfigList.stream().map(SysConfig::getCode).filter(sys -> Objects.equals(sys, SysConfigConstant.ORDER_SHOPPING_BAG))
                .filter(Objects::nonNull).collect(Collectors.toList())).orElse(new ArrayList<>());
        if (CollUtil.isEmpty(integers)) {
            return null;
        }
        List<String> collect = sysConfigList.stream().map(SysConfig::getValue).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return null;
        }
        List<Integer> ppids = new ArrayList<>();
        collect.forEach(co -> ppids.addAll(CommenUtil.toIntList(co)));
        List<Integer> newPpid = baseMapper.checkShoppingBag();
        List<Integer> oldPpid = ppids.stream().distinct().collect(Collectors.toList());
        //单差集
        Collection<Integer> subtract = CollUtil.subtract(oldPpid, newPpid);
        if (CollUtil.isEmpty(subtract)) {
            return null;
        } else {
            return subtract.stream().map(Object::toString).collect(Collectors.joining(","));
        }
    }

    /**
     * 处理获取的系统配置信息
     *
     * @param sysConfigInfo sysConfigInfo
     */
    private List<ConfigEnumVO> getLogistics(Map<Integer, List<SysConfig>> sysConfigInfo) {
        List<BaseConfigVO> logistics = new ArrayList<>();
        sysConfigInfo.forEach((key, value) -> logistics.add(new BaseConfigVO(Objects.requireNonNull(ConfigEnum.getEnumByCode(key)), false, value)));
        List<ConfigEnumVO> configEnumVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(logistics)) {
            Map<Integer, List<BaseConfigVO>> logisticsMap = logistics.stream().collect(Collectors.groupingBy(BaseConfigVO::getTitleType));
            for (Map.Entry<Integer, List<BaseConfigVO>> integerListEntry : logisticsMap.entrySet()) {
                ConfigEnumVO configInfo = new ConfigEnumVO();
                configInfo.setTitle(EnumUtil.getMessageByCode(ConfigTitleEnum.class, integerListEntry.getKey()));
                configInfo.setBaseConfigVOList(integerListEntry.getValue());
                configEnumVOList.add(configInfo);
            }
        }
        return configEnumVOList;
    }

    /**
     * 是否配置科目和辅助核算验证
     *
     * @return
     */
    @Override
    public boolean isAuxiliaryVerify() {
        //获取是否启用凭证科目辅助核算验证
        return Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.SMALL_IS_SUBJECT_VERIFY)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank).map(Convert::toBool).orElse(Boolean.FALSE);
    }


    /**
     * 获取app协议头
     *
     * @return
     */
    @Override
    public String getAppFullAppProtocolHead() {
        if (XtenantEnum.isJiujiXtenant()) {
            return "9jioa";
        }
        String result = Optional.ofNullable(sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.APP_PROTOCOL_HEAD, XtenantEnum.getXtenant()))
                .filter(R::isSuccess).map(R::getData).orElseThrow(() -> new CustomizeException("获取app协议头异常"));
        if (CommonUtils.isSaasXtenant(XtenantEnum.getXtenant())) {
            return result;
        }
        return result + "oa";
    }

    @Override
    public List<AfterExamineConfigVO> getAfterExamineList() {
        List<Integer> settingsCollect = Arrays.stream(AfterExamineEnum.values()).map(AfterExamineEnum::getCode).collect(Collectors.toList());
        //数据库里的原始配置列表
        List<SysConfig> settingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settingsCollect)) {
            List<SysConfig> sysConfigs = list(new LambdaQueryWrapper<SysConfig>().in(SysConfig::getCode, settingsCollect)
                    .orderByAsc(SysConfig::getRank));
            //根据code分组，过滤出id最小的值
            Map<Integer, List<SysConfig>> collect1 = sysConfigs.stream().collect(Collectors.groupingBy(SysConfig::getCode));
            collect1.forEach((key, value) -> {
                Optional<SysConfig> min = value.stream().filter(Objects::nonNull).min(Comparator.comparing(SysConfig::getId));
                settingList.add(min.orElse(null));
            });
        }
        if (CollectionUtils.isEmpty(settingList)) {
            return Collections.emptyList();
        }

        //数据库里的原始配置列表
        List<AfterExamineConfigVO> afterExamineConfigVOS = new ArrayList<>();
        for (SysConfig sysConfig : settingList) {
            afterExamineConfigVOS.add(sysConfigtoAfterExamineConfigVO(sysConfig));
        }

        List<AfterExamineConfigVO> result = afterExamineConfigVOS.stream().sorted(Comparator.comparing(AfterExamineConfigVO::getRank)).collect(Collectors.toList());
        return result;
    }


    /**
     * 判断value是否开启
     *
     * @param sysConfigValue
     * @param value
     * @return
     */
    boolean containConfig(Integer sysConfigValue, Integer value) {
        return (sysConfigValue & Double.valueOf(Math.pow(2, value)).intValue()) > 0;
    }

    /**
     * 通过value得到文本信息
     */
    String getConfigStringByValue(Integer sysConfigValue) {
        boolean switch1 = containConfig(sysConfigValue, 1);
        boolean switch2 = containConfig(sysConfigValue, 2);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("一审：");
        if (switch1) {
            stringBuilder.append("开,");
        } else {
            stringBuilder.append("关,");
        }


        stringBuilder.append("二审：");
        if (switch2) {
            stringBuilder.append("开");
        } else {
            stringBuilder.append("关");
        }
        return stringBuilder.toString();
    }

    /**
     * 通过开关一、二的值得到value
     *
     * @param switch1
     * @param switch2
     * @return
     */
    int computeValue(boolean switch1, boolean switch2) {
        int value = 0;
        if (switch1) {
            value += Math.pow(2, 1);
        }
        if (switch2) {
            value += Math.pow(2, 2);
        }
        return value;
    }

    AfterExamineConfigVO sysConfigtoAfterExamineConfigVO(SysConfig sysConfig) {
        AfterExamineConfigVO afterExamineConfigVO = new AfterExamineConfigVO();
        afterExamineConfigVO.setId(sysConfig.getId());
        afterExamineConfigVO.setCode(sysConfig.getCode());
        afterExamineConfigVO.setName(sysConfig.getName());
        afterExamineConfigVO.setDsc(sysConfig.getDsc());
        afterExamineConfigVO.setRank(sysConfig.getRank());
        //如果配置内容为非数字直接false
        if (StringUtils.isEmpty(sysConfig.getValue()) || !CommenUtil.isNumer(sysConfig.getValue())) {
            afterExamineConfigVO.setFirExamineSwitch(false);
            afterExamineConfigVO.setSecExamineSwitch(false);
        } else {
            //先将配置内容转为数字
            Integer sysConfigValue = Integer.valueOf(sysConfig.getValue());
            afterExamineConfigVO.setFirExamineSwitch(containConfig(sysConfigValue, 1));
            afterExamineConfigVO.setSecExamineSwitch(containConfig(sysConfigValue, 2));
        }
        afterExamineConfigVO.setFirExamineSwitchEnable(Objects.requireNonNull(AfterExamineEnum.getEnumByCode(sysConfig.getCode())).getFirExamineSwitchEnable());
        afterExamineConfigVO.setSecExamineSwitchEnable(Objects.requireNonNull(AfterExamineEnum.getEnumByCode(sysConfig.getCode())).getSecExamineSwitchEnable());
        return afterExamineConfigVO;
    }
}

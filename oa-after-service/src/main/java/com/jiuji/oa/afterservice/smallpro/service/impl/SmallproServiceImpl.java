package com.jiuji.oa.afterservice.smallpro.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.oa.oacore.oaorder.OrderDetailCloud;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.after.enums.ServiceEnum;
import com.jiuji.cloud.after.vo.req.CutScreenReq;
import com.jiuji.cloud.after.vo.req.SmallExchangeReq;
import com.jiuji.cloud.after.vo.res.ChangeInfoRes;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.apollo.ApolloKeys;
import com.jiuji.oa.afterservice.bigpro.bo.ImeiQueryInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.RecoverMkc;
import com.jiuji.oa.afterservice.bigpro.enums.AttachmentsEnum;
import com.jiuji.oa.afterservice.bigpro.enums.WuliuCateEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.impl.ShouhouExServiceImpl;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.web.req.FilmAccessoriesReq;
import com.jiuji.oa.afterservice.cloud.vo.web.res.WebFilmAccessoriesRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.bo.SubPushMsgBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.ImageProperties;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.*;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.*;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.afterservice.common.vo.res.CommonDataRes;
import com.jiuji.oa.afterservice.common.vo.res.CommonTitleRes;
import com.jiuji.oa.afterservice.common.vo.res.ListBean;
import com.jiuji.oa.afterservice.csharp.CsharpCommonService;
import com.jiuji.oa.afterservice.csharp.enums.MemberBlacklist;
import com.jiuji.oa.afterservice.csharp.vo.BlackListVo;
import com.jiuji.oa.afterservice.customeraccount.service.CustomerAccountService;
import com.jiuji.oa.afterservice.mapstruct.CommonStructMapper;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO;
import com.jiuji.oa.afterservice.other.document.SmallproLogDocument;
import com.jiuji.oa.afterservice.other.enums.CheckCodeEnum;
import com.jiuji.oa.afterservice.other.po.*;
import com.jiuji.oa.afterservice.other.service.*;
import com.jiuji.oa.afterservice.refund.enums.ZheJiaPayEnum;
import com.jiuji.oa.afterservice.refund.service.RefundMoneyService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.service.ZheJiaPayService;
import com.jiuji.oa.afterservice.small.ExchangeProductListVO;
import com.jiuji.oa.afterservice.small.SmallYuyueOrderOnGoingVO;
import com.jiuji.oa.afterservice.smallpro.bo.*;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.NotSoldBackBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProConstant;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProRelativePathConstant;
import com.jiuji.oa.afterservice.smallpro.controller.SmallproController;
import com.jiuji.oa.afterservice.smallpro.dao.AuthorizeMapper;
import com.jiuji.oa.afterservice.smallpro.dao.DiyTimeCardMapper;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.dao.TiemoCardUserLogMapper;
import com.jiuji.oa.afterservice.smallpro.enums.*;
import com.jiuji.oa.afterservice.smallpro.mapstruct.SmallproMapStruct;
import com.jiuji.oa.afterservice.smallpro.po.*;
import com.jiuji.oa.afterservice.smallpro.po.diy.DiyTimeCardPo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.ProductExchangePo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallConfigDefaultPo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeProductConfigPo;
import com.jiuji.oa.afterservice.smallpro.service.*;
import com.jiuji.oa.afterservice.smallpro.service.smallpro.SmallProConfigService;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.*;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.entity.YearPackageTransferPo;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.service.IYearPackageTransferService;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.AddSmallproLogBatchBO;
import com.jiuji.oa.afterservice.statistics.vo.req.SmallproAddLogBatchReq;
import com.jiuji.oa.afterservice.stock.enums.AreaLevelEnum;
import com.jiuji.oa.afterservice.stock.enums.StockBasketTypeEnum;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.po.ProductKcLockInfo;
import com.jiuji.oa.afterservice.stock.po.ProductMkc;
import com.jiuji.oa.afterservice.stock.service.IRecoverMkcService;
import com.jiuji.oa.afterservice.stock.service.ProductKcLockInfoService;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.stock.service.ProductMkcService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.po.SysConfig;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.loginfo.smallprobill.client.SmallproBillLogClient;
import com.jiuji.oa.loginfo.smallprobill.client.vo.SmallproBillLogVo;
import com.jiuji.oa.nc.SmsConfigCloud;
import com.jiuji.oa.nc.sms.SmsConfigCodeEnum;
import com.jiuji.oa.nc.sms.SmsConfigVO;
import com.jiuji.oa.nc.sms.SmsPushMethodEnum;
import com.jiuji.oa.oacore.csharp.cloud.CsharpCloud;
import com.jiuji.oa.oacore.csharp.vo.req.PeiJianBeiHuoAddReq;
import com.jiuji.oa.oacore.csharp.vo.req.SmallProBeihuoAutoReq;
import com.jiuji.oa.oacore.oaorder.WuliuCloud;
import com.jiuji.oa.oacore.oaorder.req.SubWLModelReq;
import com.jiuji.oa.oacore.oaorder.res.MiniFileRes;
import com.jiuji.oa.oacore.web.vo.TripartitePaymentIdReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfoBasicVO;
import com.jiuji.oa.orginfo.areainfo.vo.BigAreaQueryReq;
import com.jiuji.oa.orginfo.areainfo.vo.res.BigAreaInfoRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.area.AreaGradeEnum;
import com.jiuji.tc.utils.enums.order.ProcessBusinessTypeEnum;
import com.jiuji.tc.utils.enums.order.SubDynamicsBusinessNodeEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.swing.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SmallproServiceImpl extends ServiceImpl<SmallproMapper, Smallpro> implements SmallproService {


    @Resource
    private ImageProperties imageProperties;

    private static final Integer SCALE = 2;
    private static final Integer CONFIGID = 342;
    private static final Integer PERCENTAGE_SCALE = 4;
    private static final Integer PERCENTAGE = 100;
    private static final Integer INIT_LEN = 2;
    private static final Integer XTENANT = 1000;
    private static final String DC_SQL = "select id from areainfo  info with (nolock ) where info.authorizeid in (select id from authorize with(nolock) where dcAreaId = %s)";
    private static final String HQ_SQL = "select id from areainfo  info with (nolock )";
    private static final String H1_SQL = "select id from areainfo  info with (nolock ) where info.authorizeid in (select id from authorize with(nolock) where H1AreaId = %s)";
    private static final String D1_SQL = "select id from areainfo  info with (nolock ) where info.authorizeid in (select id from authorize with(nolock) where D1AreaId = %s)";



    /**
     * 数据总量
     */
    private static final String TOTAL = "total";
    /**
     * 导出数据页数
     */
    private static final int EXPORT_PAGES = 2;
    /**
     * 导出数据分页大小
     */
    private static final int EXPORT_PAGE_SIZE = 5000;
    // region autowried
    @Autowired
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private SmallproBillService smallproBillService;
    @Autowired
    private SmallproKindtypeService smallproKindtypeService;
    @Autowired
    private SmallproForwardExService smallproForwardExService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private SubService subService;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Autowired
    private ShouhouFanchangService shouhouFanchangService;
    @Autowired
    private AreainfoService areainfoService;
    @Autowired
    private SmallproLogService smallproLogService;
    @Autowired
    private ReceivePersonConfigService receivePersonConfigService;
    @Autowired
    @Lazy
    private SmallproService smallproService;

    @Resource
    private IYearPackageTransferService yearPackageTransferService;

    @Resource
    @Lazy
    private ShouhouExtendService extendService;

    @Autowired
    private WuliuCloud wuliuCloud;

    @Autowired
    private AuthorizeService authorizeService;
    @Autowired
    private AuthorizeMapper authorizeMapper;
    @Autowired
    private SubLogsCloud subLogsCloud;
    @Autowired
    private AttachmentsService attachmentsService;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    private CommonService commonService;
    @Autowired
    private WeixinUserService weixinUserService;
    @Resource
    private IMCloud imCloud;
    @Resource
    private SmsService smsService;
    @Resource
    private ShouhouMsgconfigService shouhouMsgconfigService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    private SysConfigService sysConfigService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private SmallProConstant smallProConstant;
    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private ProductKcService productKcService;
    @Autowired
    private AuthConfigService authConfigService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private SmallproFilmCardService smallproFilmCardService;
    @Resource
    private SmallProConfigService smallProConfigService;

    @Resource
    private CategoryService categoryService;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    private RabbitTemplate oaAsyncRabbitTemplate;
    @Resource
    private SmsConfigCloud smsConfigCloud;
    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    private BasketBindRecordService basketBindRecordService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final Integer THRESHOLD_VALUE = 500;

    @Resource
    private ApolloEntity apollo;
    /**
     * 年包商品类型
     */
    private static final Integer ANNUAL_PACKAGE_TYPE = 85;
    /**
     * 年报预警次数
     */
    private static final Integer SMALL_PRO_KIND_YEAR_CARD_NUMBER =2;
    @Qualifier("com.jiuji.oa.afterservice.cloud.service.WebCloud")
    @Autowired
    private WebCloud webCloud;
    // endregion

    // region 逻辑方法

    /**
     * 如果  返回false 那就是要不生成销售单 走原始逻辑
     *      返回true 那就是生成销售单
     * @param req
     * @return
     */
    @Override
    public HqtxhRes generateSalesOrder(HqtxhReq req){
        HqtxhRes hqtxhRes = new HqtxhRes();
        //只有九机才开始走这个逻辑 而且只有换货走这个逻辑
        if(XtenantEnum.isSaasXtenant() || !SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(req.getKind())){
            return hqtxhRes;
        }
        if (openAreaId(req.getSmallproAreaId())) {
            return hqtxhRes;
        }
        //多个接件标记
        boolean multipleConnectorsFlag = false;
        //出险年包服务标记
        boolean annualPackageFlag = false;
        //相同ppid商品换货标记
        boolean theSamePpidFlag = false;
        //大件附件换货标记
        boolean annexFlag = false;
        //订单存在年包服务商品
        boolean existAnnualPackage = false;
        //接件商品是否为壳膜分类
        boolean filmAndCase =false;
        //绑定服务商品
        boolean filmCardInfoByBasketId =false;
        //判断是否为历史订单
        boolean isHistoryOrder = false;
        //判断小件单是否存在小件换货
        boolean isExistSmallPro = false;
        //小件换货
        Integer smallProId = req.getSmallProId();
        if(ObjectUtil.isNotNull(smallProId)){
            List<ShouhouTuihuan> list = SpringUtil.getBean(ShouhouTuihuanService.class).lambdaQuery().eq(ShouhouTuihuan::getSmallproid, smallProId)
                    .eq(ShouhouTuihuan::getTuihuanKind, TuihuanKindEnum.SMALL_PRO_REFUND.getCode())
                    .select(ShouhouTuihuan::getId)
                    .last("and isnull(isdel,0)=0")
                    .list();
            isExistSmallPro = CollectionUtils.isNotEmpty(list);
        }
        // 接件商品
        List<ConnectorProductDetail> detailList = req.getConnectorProductDetailList();
        if(CollectionUtils.isEmpty(detailList)){
            throw new CustomizeException("接件商品不能为空");
        }
        ConnectorProductDetail detail = detailList.get(0);
        // 判断如果接件数量是 1 那就生成销售单 或者是进行多个商品接件
        if(detailList.size() != NumberConstant.ONE || !Optional.ofNullable(detail.getConnectorPpidCount()).orElse(NumberConstant.ZERO).equals(NumberConstant.ONE)){
            multipleConnectorsFlag = true;
        }
        // 出险年包服务
        Integer serviceType = req.getServiceType();
        if(SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode().equals(serviceType)){
            annualPackageFlag = true;
        }
        //相同ppid商品换货
        Integer changePpriceid = Optional.ofNullable(req.getChangePpid()).orElse(Integer.MIN_VALUE);
        Integer ppriceid = Optional.ofNullable(detail.getConnectorPpid()).orElse(NumberConstant.ZERO);
        if(ppriceid.equals(changePpriceid)){
            theSamePpidFlag=true;
        }
        Integer mobileExchangeFlag = Optional.ofNullable(detail.getMobileExchangeFlag()).orElse(NumberConstant.ZERO);
        if(mobileExchangeFlag.equals(NumberConstant.ONE)){
            annexFlag=true;
        }
        //判断订单商品里面是否存在年包
        OaUserBO currentStaff = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登陆信息超时"));
        //非HQ地区，输出系统数据需要隔离
        Boolean authPart = authConfigService.isAuthPart(currentStaff);
        if (authPart) {
            AreaBelongsDcHqD1AreaId backInfo = areainfoService.getAreaBelongsDcHqD1AreaId(currentStaff.getAreaId());
            //非HQ地区，数据需要隔离,排除九机
            if (backInfo != null && currentStaff.getXTenant() >= ConfigConsts.TENANT_THRESHOLD && !CollUtil.contains(backInfo.getHqAreaIds(), currentStaff.getAreaId())) {
                authPart = Boolean.TRUE;
            }
        }
        List<SmallproReceivableProductBO> list = Optional.ofNullable(
                this.baseMapper.getSmallproReceivableProductBySubIdV3(req.getSubId(), DateUtil.localDateTimeToString(SpringUtil.getBean(SmallProAdapterService.class).getFrameTime()), authPart, currentStaff.getAuthorizeId()))
                .orElseGet(ArrayList::new);
        if(CollectionUtils.isNotEmpty(list)){
            for (SmallproReceivableProductBO item: list) {
                Integer tieMoId = item.getTieMoId();
                Integer basketType = item.getBasketType();
                if(ObjectUtil.isNotNull(tieMoId) || ANNUAL_PACKAGE_TYPE.equals(basketType)){
                    existAnnualPackage = true;
                    break;
                }
            }
        }
        //判断商品是否为壳 膜分类的 如果是壳膜分类的情况下不走换其他型号
        filmAndCase = categoryService.determineShellAndFilmByPpid(ppriceid);
        //判断接件商品有没有在tiemocard表里面
        Integer basketId = Optional.ofNullable(detail.getBasketId()).orElse(NumberConstant.ZERO);
        FilmCardInfomationBO filmCardInfoByBasketIdByWrite = SpringUtil.getBean(SmallproFilmCardService.class).getFilmCardInfoByBasketIdByWrite(basketId);
        if(ObjectUtil.isNotNull(filmCardInfoByBasketIdByWrite)){
            filmCardInfoByBasketId = true;
        }
        //判断是否为历史订单
        if(!NumberConstant.ZERO.equals(basketId)){
            isHistoryOrder = MTableInfoEnum.BASKET.isHistory(basketId.longValue());
        }
        hqtxhRes.setGenerateSalesOrder(!(multipleConnectorsFlag||annualPackageFlag||theSamePpidFlag||annexFlag||existAnnualPackage||filmCardInfoByBasketId || filmAndCase ||isHistoryOrder ||isExistSmallPro))
                .setAnnexFlag(annexFlag)
                .setMultipleConnectorsFlag(multipleConnectorsFlag)
                .setAnnualPackageFlag(annualPackageFlag)
                .setFilmAndCase(filmAndCase)
                .setIsHistoryOrder(isHistoryOrder)
                .setFilmCardInfoByBasketId(filmCardInfoByBasketId)
                .setTheSamePpidFlag(theSamePpidFlag);
        return hqtxhRes;

    }


    @Override
    public PickUpCheckRes pickUpCheck(PickUpCheckReq pickUpCheckReq) {
        Integer smallProId = pickUpCheckReq.getSmallProId();
        PickUpCheckRes pickUpCheckRes = new PickUpCheckRes();
        pickUpCheckRes.setCheckCode(CheckCodeEnum.SUCCESS.getCode());
        pickUpCheckRes.setSmallProId(smallProId);
        Smallpro smallpro = Optional.ofNullable(this.getByIdSqlServer(smallProId)).orElseThrow(()->new CustomizeException("小件单查询为空"));
        //灰度控制
        if(!isNewSmallPro(smallpro.getAreaId())){
            return pickUpCheckRes;
        }
        List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallProId).list();
        if(CollectionUtils.isEmpty(smallproBillList)){
            throw new CustomizeException("接件商品为空");
        }
        Integer kind = smallpro.getKind();
        //判断如果 不是九机或者是换其他型号或者不是换货类型  那就直接返回成功
        if(!XtenantEnum.isJiujiXtenant() || isHqtxh(smallProId) || !SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(kind)){
            return pickUpCheckRes;
        }
        //判断是否走贴膜流程 并且 不需要付款和有存库的情况下 那就直接返回成功
        SmallproBill smallproBill = Optional.ofNullable(smallproBillList.get(NumberConstant.ZERO)).orElse(new SmallproBill());
        if(categoryService.determineFilmByPpid(Convert.toInt(smallproBill.getPpriceid()))){
            //判断是否需要收银
            List<SmallproPickUpOutboundInfoBO> outboundInfoList = this.baseMapper.getOutboundInfo(smallpro.getId());
            if(CollectionUtils.isNotEmpty(outboundInfoList)){
                //计算已付金额
                List<Shouying> shouyings = SpringUtil.getBean(ShouyingService.class).lambdaQuery().eq(Shouying::getSubId, smallProId)
                        .in(Shouying::getShouyingType, RefundMoneyUtil.getShouyingTypes(TuihuanKindEnum.SMALL_PRO_REFUND_REPAIR_FEE))
                        .list();
                BigDecimal yifum = shouyings.stream().map(Shouying::getHejim).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal feiYong = Optional.ofNullable(smallpro.getFeiyong()).orElse(BigDecimal.ZERO);
                // 如果库存大于0  并且 不需要收银 那就返回成功
                SmallproPickUpOutboundInfoBO smallproPickUpOutboundInfoBO = Optional.ofNullable(outboundInfoList.get(NumberConstant.ZERO)).orElse(new SmallproPickUpOutboundInfoBO());
                Integer leftCount = Optional.ofNullable(productKcService.getKcCount(smallproPickUpOutboundInfoBO.getPpriceId1(), smallpro.getAreaId())).orElse(NumberConstant.ZERO);
                //如果库存为0的时候判断是否为本身小件单自己锁定
                if(NumberConstant.ZERO.equals(leftCount)){
                    List<ProductKcLockInfo> kcLockInfos = SpringUtil.getBean(ProductKcLockInfoService.class).lambdaQuery()
                            .eq(ProductKcLockInfo::getBasketId, smallproBill.getId())
                            .eq(ProductKcLockInfo::getIsDone, Boolean.FALSE)
                            .eq(ProductKcLockInfo::getIsDel, Boolean.FALSE)
                            .eq(ProductKcLockInfo::getBasketType, StockBasketTypeEnum.SMALL_PRODUCT_ORDER.getCode())
                            .list();
                    if(CollectionUtils.isNotEmpty(kcLockInfos)){
                        Integer reduce = kcLockInfos.stream().map(ProductKcLockInfo::getLockCount)
                                .filter(Objects::nonNull)
                                .reduce(NumberConstant.ZERO, Integer::sum);
                        leftCount = leftCount + reduce;
                    }
                }
                if(ObjectUtil.compare(feiYong,yifum) == 0 && leftCount>0){
                    return pickUpCheckRes;
                }
            }
        }
        //判断如果没有进行扫码验证那就需要扫码取件验证
        List<String> rankList = Optional.ofNullable(Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElse(new OaUserBO()).getRank()).orElse(new ArrayList<>());
        Boolean scanFlag = Optional.ofNullable(smallproBill.getScanFlag()).orElse(Boolean.FALSE);
        if(!rankList.contains("smqj") && Boolean.FALSE.equals(scanFlag)){
            pickUpCheckRes.setCheckCode(CheckCodeEnum.NOT_SCANNED.getCode());
            return pickUpCheckRes;
        }
        //判断是否需要用户验证
        if (StrUtil.isEmpty(smallpro.getCodeMsg())) {
            pickUpCheckRes.setCheckCode(CheckCodeEnum.NOT_VERIFIED.getCode());
            return pickUpCheckRes;
        }
        return pickUpCheckRes;
    }

    /**
     *      * 第一：如果过为空那就是全部门店使用，
     *      * 第二：如果存在具体门店id那就这几个门店id使用
     *      * 第三：如果只存在-1 那就是所有门店不使用
     * @param areaId
     * @return
     */
    @Override
    public Boolean isNewSmallPro(Integer areaId) {
        List<Integer> list = new ArrayList<>();
        if (XtenantEnum.isSaasXtenant()) {
           return Boolean.FALSE;
        } else {
            String areaIdList = Optional.ofNullable(apolloEntity.getSmallProNewAreaId()).orElse("");
            list = Arrays.stream(areaIdList.split(",")).filter(StringUtils::isNotEmpty).map(Integer::valueOf).collect(Collectors.toList());
        }
        //如果过为空那就是全部门店使用，
        if(CollectionUtils.isEmpty(list)){
            return Boolean.TRUE;
        }
        return list.contains(areaId);
    }
    @Override
    public void fixOldUserId() {
//        List<Smallpro> needUpdateList = this.lambdaQuery()
//                .eq(Smallpro::getOldIdType, OldIdTypeEnum.SMALL_PRO_TYPE.getCode())
//                .select(Smallpro::getOldId, Smallpro::getId)
//                .list();
//        if(CollectionUtils.isEmpty(needUpdateList)){
//            throw new CustomizeException("需要修改的数据为空");
//        }
//        List<Integer> smallProIdList = needUpdateList.stream().map(Smallpro::getOldId).collect(Collectors.toList());
//        Map<Integer, Integer> map = CommonUtils.bigDataInQuery(smallProIdList, ids -> this.lambdaQuery().in(Smallpro::getId, ids).select(Smallpro::getId, Smallpro::getUserId).list())
//                .stream().collect(Collectors.toMap(Smallpro::getId, Smallpro::getUserId, (n1, n2) -> n2));
//        StringJoiner joiner = new StringJoiner(",");
//        needUpdateList.forEach(item->{
//            Integer userId = map.get(item.getOldId());
//            if(ObjectUtil.isNotNull(userId)){
//                boolean updated = this.lambdaUpdate().eq(Smallpro::getId, item.getId())
//                        .set(Smallpro::getOldUserId,userId)
//                        .update();
//                if(!updated){
//                    throw new CustomizeException("小件单："+item.getId()+"修改为："+userId+"失败");
//                }
//            } else {
//                joiner.add(item.getId().toString());
//            }
//
//        });
//        log.warn("小件单没有找到修改信息："+joiner);
    }

    /**
     * 获取损耗数量
     * @param oldIdType
     * @param oldId
     * @return
     */
    @Override
    public Integer selectQuantityOfLoss(Integer oldIdType, Integer oldId) {
        return Optional.ofNullable(baseMapper.findQuantityOfLoss(oldIdType,oldId)).orElse(NumberConstant.ZERO);
    }

    /**
     * 损耗库存检测
     * @param ppid
     * @param ppidCount
     * @param areaId
     */
    private void checkLossKc(Integer ppid,Integer ppidCount,Integer areaId){
        // 校验库存需要用, ppid1进行校验
        Productinfo productinfo = productinfoService.getProductinfoByPpid(ppid);
        Integer kcCount = Optional.ofNullable(productKcService.getKcCount(productinfo.getPpriceid1(), areaId)).orElse(NumberConstant.ZERO);
        if(kcCount<ppidCount){
            throw new CustomizeException("库存不足，请先补货，ppid："+ppid+"需要的库存为："+ppidCount);
        }
    }


    /**
     * 小件单生成现货单
     * @param req
     * @return
     */

    @Override
    public AutGeneratedCashRes autGeneratedCash(AutGeneratedCashReq req) {
        AutGeneratedCashRes autGeneratedCashRes = new AutGeneratedCashRes();
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录失效"));
        Integer ppid = req.getPpid();
        checkLossKc(ppid,req.getPpidCount(),userBO.getAreaId());
        //创建生成现货单所需要参数
        SmallproReq smallproReq = generatedCashReq(req);
        log.warn("自动生成现货单入参：{},创建参数：{}", JSONUtil.toJsonStr(smallproReq),JSONUtil.toJsonStr(smallproReq));
        SmallproController smallproController = SpringUtil.getBean(SmallproController.class);
        //判断损耗商品是否为膜
        Boolean determineFilmByPpid = Optional.ofNullable(categoryService.determineFilmByPpid(ppid)).orElse(Boolean.FALSE);
        if(!determineFilmByPpid){
            throw new CustomizeException("只能对“保护膜”品类商品提交贴膜损耗");
        }
        //生成现货单
        R result = smallproController.saveSmallpro(smallproReq);
        if(!result.isSuccess()){
            throw new CustomizeException(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
        }
//        //去生成现货单日志记录
//        SmallproReq data = (SmallproReq) result.getData();
//        //在原来的小件单上面添加生成现货单日志
//        Integer smallProIdOld = req.getSmallProId();
//        String orderUrl = String.format("<a href= /staticpc/#/small-refund/%s>%s</a>",data.getId(),data.getId());
//        String msg = String.format("生成小件现货单：%s", orderUrl);
//        smallproLogService.addLogs(smallProIdOld, msg, userBO.getUserName(), 0);
//        //在生成的现货单上记录日志
//        Integer smallProId = data.getId();
//        String url = "/staticpc/#/small-refund/"+req.getSmallProId();
//        String message = String.format("小件售前接件，单号"+"<a href="+url+">"+req.getSmallProId() +"</a>");
//        smallproLogService.addLogs(smallProId, message, userBO.getUserName(), 0);
//        //完成现货单
//        SmallproExchangePurchaseService exchangePurchaseService = SpringUtil.getBean(SmallproExchangePurchaseService.class);
//        PickUpExtendReq pickUpExtendReq = new PickUpExtendReq();
//        pickUpExtendReq.setLossCount(req.getPpidCount());
//        pickUpExtendReq.setOperationKind("小件单生成现货单自动完成");
//        pickUpExtendReq.setPpid(req.getPpid());
//        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes = exchangePurchaseService.pickup(smallProId,SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode() , userBO,pickUpExtendReq);
//        if (smallproNormalCodeMessageRes.getCode().equals(500)) {
//            log.warn("小件单生成的现货单取件失败" + smallProId + ":" + smallproNormalCodeMessageRes.getMessage());
//            throw new CustomizeException(smallproNormalCodeMessageRes.getMessage());
//        }
        return autGeneratedCashRes;
    }

    /**
     * 销售单生成现货单
     * @param req
     * @return
     */
    @Override
    public AutGeneratedCashRes autGeneratedCashByBasketId(AutGeneratedCashByBasketIdReq req) {
        AutGeneratedCashRes autGeneratedCashRes = new AutGeneratedCashRes();
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录失效"));
        Integer ppid = req.getPpid();
        checkLossKc(ppid,req.getPpidCount(),userBO.getAreaId());
        //创建生成现货单所需要参数
        SmallproReq smallproReq = generatedCashReqByBasketId(req);
        log.warn("销售单自动生成现货单入参：{},创建参数：{}", JSONUtil.toJsonStr(smallproReq),JSONUtil.toJsonStr(smallproReq));
        SmallproController smallproController = SpringUtil.getBean(SmallproController.class);
        //生成现货单
        R result = smallproController.saveSmallpro(smallproReq);
        if(!result.isSuccess()){
            throw new CustomizeException(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
        }
        Optional.ofNullable(result.getData()).ifPresent(item->{
            if(item instanceof SmallproReq){
                SmallproReq bean = JSONUtil.toBean(JSONUtil.toJsonStr(item), SmallproReq.class);
                autGeneratedCashRes.setSmallProId(bean.getId());
            }
        });
        return autGeneratedCashRes;
    }

    @Override
    public SelectKcCountRes selectKcCount(SelectKcCountReq req) {
        SelectKcCountRes selectKcCountRes = new SelectKcCountRes();
        Integer ppid = req.getPpid();
        selectKcCountRes.setPpid(ppid);
        Productinfo productinfo = productinfoService.getProductinfoByPpid(ppid);
        Integer kcCount = this.baseMapper.getKcCount(productinfo.getPpriceid1(),req.getAreaId());
        selectKcCountRes.setKcCount(kcCount);
        //获取商品图片
        Map<Integer, PictureInfo> pictureInfoMap = productinfoService.selectPictureByEntityMap(Collections.singletonList(productinfo));
        PictureInfo pictureInfo = pictureInfoMap.getOrDefault(ppid, new PictureInfo());
        selectKcCountRes.setProductImageUrl(pictureInfo.getProductImageUrl());
        selectKcCountRes.setProductInfoUrl(pictureInfo.getProductInfoUrl());
        //商品基本信息封装
        Optional.ofNullable(productinfo).ifPresent(item->{
            selectKcCountRes.setProductName(item.getProductName());
            selectKcCountRes.setProductColor(item.getProductColor());
            selectKcCountRes.setBarCode(item.getBarCode());
        });
        return selectKcCountRes;
    }

    /**
     * 封装商品类型
     * @param req
     */
    private void createProductType(SelectLossInfoReq req){
        Integer basketId = req.getBasketId();
        Integer smallProId = req.getSmallProId();
        Integer productBindType = NumberConstant.ZERO;
        if (ObjectUtil.isNull(basketId) && ObjectUtil.isNotNull(smallProId) ) {
            List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallProId).list();
            if(CollectionUtils.isEmpty(smallproBillList)){
                throw new CustomizeException("小件接件商品为空");
            }
            //查询是否绑定串号
            productBindType = handleProductBindType(smallproBillList.get(NumberConstant.ZERO).getBasketId());
        } else if (ObjectUtil.isNotNull(basketId) && ObjectUtil.isNull(smallProId)){
            productBindType = handleProductBindType(basketId);
        } else {
            throw new CustomizeException("basketId 和 smallProId 不能同时有值或者没有值");
        }
        req.setProductType(productBindType);
    }

    @Override
    public Integer handleProductBindType(Integer basketId){
        Integer productBindType = NumberConstant.ZERO;
        //销售单处理逻辑
        Basket basket =Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.lambdaQuery().eq(Basket::getBasketId, basketId).one(), MTableInfoEnum.BASKET, basketId)).orElseThrow(()->new CustomizeException("订单商品查询为空"));
        LocalDateTime basketDate = basket.getBasketDate();
        LocalDateTime inDate = basketDate.plusHours(24L);
        List<BasketBindRecord> basketBindRecordList = CommenUtil.autoQueryHist(()-> SpringUtil.getBean(BasketBindRecordService.class).lambdaQuery()
                .eq(BasketBindRecord::getBasketId, basket.getBasketId())
                .list());
        boolean overTime = inDate.isBefore(LocalDateTime.now());
        //商品没有绑定串号且已超过接件时间24h
        if(CollectionUtils.isEmpty(basketBindRecordList) && overTime){
            productBindType = ProductBindTypeEnum.OVER_TIME.getCode();
        }
        //商品绑定串号，或者商品没有绑定串号但在加单时间24h内。
        if(CollectionUtils.isNotEmpty(basketBindRecordList) || !overTime){
            productBindType = ProductBindTypeEnum.NOT_OVER_TIME.getCode();
        }
        return productBindType;
    }


    /**
     *
     * @param automaticBindImeiReq
     */
    @Override
    public void automaticBindImei(AutomaticBindImeiReq automaticBindImeiReq){
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/oaApi.svc/rest/SaveBasketBindRecord";
        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                .body(JSONUtil.toJsonStr(automaticBindImeiReq))
                .execute();
        log.warn("自动绑定串号：{}，返回结果：{}",JSONUtil.toJsonStr(automaticBindImeiReq),evidenceResult.body());
        if(evidenceResult.isOk()){
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(!result.isSuccess()){
                throw new CustomizeException("自动绑定串号失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("自动绑定串号接口异常传入参数：{}",JSONUtil.toJsonStr(automaticBindImeiReq));
            throw new CustomizeException("自动绑定串号异常");
        }
    }


    @Override
    public SelectKcCountRes selectLossInfo(SelectLossInfoReq req) {
        //通过basketId获取ppid和areaId
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息失效，请重新登录"));
        //封装productType
        createProductType(req);
        //进行串号绑定的验证
        String imei = req.getImei();
        Integer basketId = Optional.ofNullable(req.getBasketId()).orElseGet(()->{
            Integer smallProId = req.getSmallProId();
            List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallProId).list();
            if(CollectionUtils.isNotEmpty(smallproBillList)){
                SmallproBill smallproBill = smallproBillList.get(NumberConstant.ZERO);
                return smallproBill.getBasketId();
            }
            return NumberConstant.ZERO;
        });
        List<BasketBindRecord> list  = CommenUtil.autoQueryHist(()-> basketBindRecordService.lambdaQuery()
                .eq(BasketBindRecord::getBasketId, basketId)
                .list());
        //如果是basketId存在串号绑定的情况  那就对比是绑定一致
        List<YearPackageTransferPo> transferPos = yearPackageTransferService.lambdaQuery().eq(YearPackageTransferPo::getTransferCode, imei).list();
        if(CollectionUtils.isNotEmpty(list)){
            List<String> imeiList = list.stream().map(BasketBindRecord::getImei).collect(Collectors.toList());
            if(!imeiList.contains(imei)){
                if(CollUtil.isEmpty(transferPos)){
                    log.warn("扫码串号和绑定串号不一致 传入串号：{}，数据库串号：{}", imei,imeiList);
                    throw new CustomizeException("扫码串号和绑定串号不一致");
                }

            }

        } else {
            //虚拟串号跳过这段逻辑
            if(CollUtil.isNotEmpty(transferPos)){
                //如果是没有绑定串号的情况 那就校验加单时间有没有超过24小时
                Integer productType = req.getProductType();
                if(NumberConstant.ONE.equals(productType)){
                    //没有超过24小时情况下 自动进行串号绑定
                    AutomaticBindImeiReq automaticBindImeiReq = new AutomaticBindImeiReq();
                    automaticBindImeiReq.setBindKind(NumberConstant.TWO);
                    automaticBindImeiReq.setImei(imei);
                    automaticBindImeiReq.setBasket_id(basketId);
                    automaticBindImeiReq.setBinduser(userBO.getUserName());
                    automaticBindImei(automaticBindImeiReq);
                } else {
                    //超过24小时
                    throw new CustomizeException("该订单没有进行串号绑定");
                }
            }

        }
        //封装查询条件
        Basket basket =Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.lambdaQuery()
                .eq(Basket::getBasketId, basketId).one(), MTableInfoEnum.BASKET, basketId))
                .orElseThrow(()->new CustomizeException("订单商品查询为空"));
        SelectKcCountReq selectKcCountReq = new SelectKcCountReq();
        selectKcCountReq.setAreaId(userBO.getAreaId());
        Integer smallProId = req.getSmallProId();
        //如果是小件单情况下那就获取置换ppid
        if(ObjectUtil.isNotNull(smallProId)){
            Smallpro smallpro = Optional.ofNullable(this.getById(smallProId)).orElseThrow(() -> new CustomizeException("小件单查询为空"));
            selectKcCountReq.setPpid(smallpro.getChangePpriceid());
        } else {
            selectKcCountReq.setPpid(Convert.toInt(basket.getPpriceid()));
        }
        SelectKcCountRes selectKcCountRes = selectKcCount(selectKcCountReq);
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        StringBuilder jumpConnection = new StringBuilder(host+"/new/#/small-refund/new?");
        if(Arrays.asList("小件售后单","订单详细页").contains(req.getConnectionMethod())){
            jumpConnection.append(String.format("xianHuoSubId=%s&ppriceId=%s&connectionMethod=%s&imei=%s&smallProId=%s&selectBasketId=%s",basket.getSubId(),basket.getPpriceid(),req.getConnectionMethod(),imei,smallProId,basket.getBasketId()));
        } else {
            jumpConnection.append(String.format("sub_id=%s&ppriceId=%s&connectionMethod=%s&imei=%s&selectBasketId=%s",basket.getSubId(),basket.getPpriceid(),req.getConnectionMethod(),imei,basket.getBasketId()));
        }
        selectKcCountRes.setJumpConnection(jumpConnection.toString());
        return selectKcCountRes;
    }

    @Override
    public BindImeiRes selectBindImei(BindImeiReq req) {
        BindImeiRes bindImeiRes = new BindImeiRes();
        List<BasketBindRecord> list = CommenUtil.autoQueryHist(()-> basketBindRecordService.lambdaQuery()
                .eq(BasketBindRecord::getBasketId, req.getBasketId())
                .list());
        if(CollectionUtils.isNotEmpty(list)){
            bindImeiRes.setImeiList(list.stream().map(BasketBindRecord::getImei).collect(Collectors.toList()));
        }
        return bindImeiRes;
    }

    /**
     * 小件取机 并且完成小件现货单
     * @param req
     * @return
     */
    @Override
    public R<Boolean> autGeneratedCashAndPickUp(AutGeneratedCashReq req) {
        Integer kind = Optional.ofNullable(req.getKind()).orElseThrow(() -> new CustomizeException("请选处理类型类型"));
        SmallproController smallproController = SpringUtil.getBean(SmallproController.class);
        R<Boolean> result = smallproController.pickup(req.getSmallProId(), kind,req.getTransferAreaId());
        try {
            //取机成功之后创建现货单
            if(result.isSuccess()){
                smallproService.autGeneratedCash(req);
            }
        } catch (Exception e){
            RRExceptionHandler.logError("小件取机自动完成现货单异常", req, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        return result;
    }

    /**
     * 创建生成现货单所需要参数
     * @param req
     * @return
     */
    private SmallproReq generatedCashReq(AutGeneratedCashReq req){
        SmallproReq smallproReq = new SmallproReq();
        //小件单查询
        Integer smallProId = req.getSmallProId();
        Smallpro smallpro = this.getById(smallProId);
        //小件单校验是否可以生成现货单
          Boolean isGenerateCashOrder = SpringUtil.getBean(SmallproDetailsExService.class).createIsGenerateCashOrder(smallpro);
        if(!isGenerateCashOrder){
            throw new CustomizeException("该小件单不能生成现货单");
        }
        smallproReq.setOldId(smallProId);
        smallproReq.setOldIdType(OldIdTypeEnum.SMALL_PRO_TYPE.getCode());
        smallproReq.setGroupId(smallpro.getGroupId());
        smallproReq.setKind(SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode());
        smallproReq.setIsBaoxiu(smallpro.getIsBaoxiu());
        smallproReq.setArea(smallpro.getArea());
        smallproReq.setLossCount(req.getPpidCount());
        smallproReq.setLossPpid(req.getPpid());
        smallproReq.setAreaId(smallpro.getAreaId());
        smallproReq.setUserId(BbsxpUserIdConstants.XIAN_HUO);
        smallproReq.setUserName(BbsxpUserIdConstants.XIAN_HUO_NAME);
        smallproReq.setBuyDate(smallpro.getBuyDate());
        smallproReq.setStats(SmallProStatsEnum.SMALL_PRO_STATS_PROCESSING.getCode());
        smallproReq.setIsSpecialTreatment(Boolean.FALSE);
        smallproReq.setName(smallpro.getName());
        smallproReq.setMobile("00000000000");
        smallproReq.setImei(req.getImei());
        smallproReq.setSubId(NumberConstant.ZERO);
//        smallproReq.setProblem(String.format("贴膜损耗，小件单号：%s", smallProId));
        smallproReq.setProblem("贴膜损耗");

        String comment = req.getComment();
        //您于XXX月XXX日在XXXX店进行贴膜服务，贴膜过程中，因产品瑕疵或操作失误，产生了XXXX张贴膜损耗，此损耗由九机全额承担，不影响您的贴膜次数
        LocalDateTime qujianDate = LocalDateTime.now();
        Integer num = Optional.ofNullable(req.getPpidCount()).orElse(1);
        Areainfo areainfo = Optional.ofNullable(areainfoService.getByIdSqlServer(smallpro.getAreaId())).orElse(new Areainfo());
        String smsPrintName = CommenUtil.getSmsPrintName(areainfo.getPrintName());
        //确认过 是替换
        comment = StrUtil.format("您于{}月{}日在{}进行贴膜服务，贴膜过程中，因产品瑕疵或操作失误，产生了{}张贴膜损耗，此损耗由{}全额承担，不影响您的贴膜次数",
                qujianDate.getMonthValue(), qujianDate.getDayOfMonth(), areainfo.getAreaName(), num, smsPrintName);
        smallproReq.setComment(comment);

        smallproReq.setConnectionMethod("保护膜旧件生成现货单");
        smallproReq.setIsCutScreenSpotFlag(Boolean.FALSE);
        smallproReq.setFileResReqImeiList(req.getFileResReqList());
        //接件信息构造
        SmallproBill smallproBill = new SmallproBill();
        smallproBill.setCount(req.getPpidCount())
                .setPpriceid(Optional.ofNullable(req.getPpid()).orElse(NumberConstant.ZERO).longValue())
                .setMobileExchangeFlag(NumberConstant.ZERO);
        AtomicReference<Integer> basketIdAtomicReference = new AtomicReference<>(NumberConstant.ZERO);
        List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallProId).list();
        //判断只有ppid相同的情况下才进行basketId的赋值
        if(CollectionUtils.isNotEmpty(smallproBillList)){
            smallproBillList.stream().filter(item->Optional.ofNullable(req.getPpid()).orElse(NumberConstant.ZERO).equals(item.getPpriceid()))
                    .findFirst().ifPresent(item->basketIdAtomicReference.getAndSet(item.getBasketId()));
        }
        smallproBill.setBasketId(basketIdAtomicReference.get());
        smallproReq.setSmallproBillList(Collections.singletonList(smallproBill));
        return smallproReq;
    }
    /**
     * 创建生成现货单所需要参数
     * @param req
     * @return
     */
    private SmallproReq generatedCashReqByBasketId(AutGeneratedCashByBasketIdReq req){
        SmallproReq smallproReq = new SmallproReq();
        Integer ppid = req.getPpid();
        //商品分类校验
        if(!categoryService.determineFilmByPpid(ppid)){
            throw new CustomizeException("当前商品不属于膜分类");
        }
        //销售单查询
        Integer basketId = req.getBasketId();
        Basket basket = Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.lambdaQuery().eq(Basket::getBasketId, basketId)
                                .last(" and isnull(isdel,0)=0").one(),
                        MTableInfoEnum.BASKET, basketId)).orElseThrow(() -> new CustomizeException("订单商品查询为空"));
        Sub sub = Optional.ofNullable(CommenUtil.autoQueryHist(() -> subService.getById(basket.getSubId()), MTableInfoEnum.SUB, basket.getSubId())).orElseThrow(() -> new CustomizeException("订单查询为空")) ;
        //商品信息查询
        Productinfo productinfo = Optional.ofNullable(productinfoService.getProductinfoByPpid(ppid)).orElse(new Productinfo());
        Areainfo areainfo = Optional.ofNullable(areainfoService.getByIdSqlServer(sub.getAreaId())).orElse(new Areainfo());
        smallproReq.setOldId(sub.getSubId());
        smallproReq.setOldIdType(OldIdTypeEnum.SALE_TYPE.getCode());
        smallproReq.setGroupId(SmallproGroupEnum.MOBILE_PHONE_PARTS.getGroupId());
        smallproReq.setKind(SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode());
        smallproReq.setIsBaoxiu(Boolean.TRUE);
        smallproReq.setArea(areainfo.getArea());
        smallproReq.setLossCount(req.getPpidCount());
        smallproReq.setLossPpid(req.getPpid());
        smallproReq.setAreaId(sub.getAreaId());
        smallproReq.setImei(req.getImei());
        smallproReq.setUserId(BbsxpUserIdConstants.XIAN_HUO);
        smallproReq.setUserName(BbsxpUserIdConstants.XIAN_HUO_NAME);
        smallproReq.setBuyDate(sub.getSubDate());
        smallproReq.setStats(SmallProStatsEnum.SMALL_PRO_STATS_PROCESSING.getCode());
        smallproReq.setIsSpecialTreatment(Boolean.FALSE);
        smallproReq.setName(productinfo.getProductName());
        smallproReq.setMobile("00000000000");
        smallproReq.setSubId(sub.getSubId());
//        smallproReq.setProblem(String.format("订单贴膜售前损耗，订单号：%s，ppid：%s", sub.getSubId(),ppid));
        smallproReq.setProblem("贴膜损耗");
        smallproReq.setConnectionMethod(req.getConnectionMethod());
        smallproReq.setIsCutScreenSpotFlag(Boolean.FALSE);
        smallproReq.setFileResReqImeiList(req.getFileResReqList());

        String comment = req.getComment();
        //您于XXX月XXX日在XXXX店进行贴膜服务，贴膜过程中，因产品瑕疵或操作失误，产生了XXXX张贴膜损耗，此损耗由九机全额承担，不影响您的贴膜次数
        LocalDateTime qujianDate = LocalDateTime.now();
        Integer num = Optional.ofNullable(req.getPpidCount()).orElse(1);
        String smsPrintName = CommenUtil.getSmsPrintName(areainfo.getPrintName());
        //确认过 是替换
        comment = StrUtil.format("您于{}月{}日在{}进行贴膜服务，贴膜过程中，因产品瑕疵或操作失误，产生了{}张贴膜损耗，此损耗由{}全额承担，不影响您的贴膜次数",
                qujianDate.getMonthValue(), qujianDate.getDayOfMonth(), areainfo.getAreaName(), num, smsPrintName);
        smallproReq.setComment(comment);

        //接件信息构造
        SmallproBill smallproBill = new SmallproBill();
        smallproBill.setCount(req.getPpidCount())
                .setPpriceid(Optional.ofNullable(req.getPpid()).orElse(NumberConstant.ZERO).longValue())
                .setMobileExchangeFlag(NumberConstant.ZERO);
        Integer basketIdBill = NumberConstant.ZERO;
        //判断只有ppid相同的情况下才进行basketId的赋值
        if(Convert.toInt(Optional.ofNullable(basket.getPpriceid()).orElse(0L)).equals(ppid)){
            basketIdBill = basketId;
        }
        smallproBill.setBasketId(basketIdBill);
        smallproReq.setSmallproBillList(Collections.singletonList(smallproBill));
        return smallproReq;
    }

    @Override
    public String secondaryConfirmation(SmallproReq smallpro) {
        //1.近一个月内兑换年包次数超过2次及以上 按照年包（当前使用的年包）维度进行查询统计（需要处理正常年包+diy年包）
        //正常年包处理
        List<SmallproBill> smallproBillList = smallpro.getSmallproBillList();
        if(CollectionUtils.isEmpty(smallproBillList)){
            return "";
        }
        SmallproBill smallproBill = smallproBillList.get(0);
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMonths(1L);
        if (SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode().equals(smallpro.getServiceType())
                && smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode())
                && CollectionUtils.isNotEmpty(smallproBillList) && smallproBillList.size() == 1) {
            List<TiemoCardUserLog> tiemoCardUserLogs = SpringUtil.getBean(TiemoCardUserLogMapper.class).selectLogByBasketId(smallproBill.getBasketId(), startTime, endTime);
            if(tiemoCardUserLogs.size()>=SMALL_PRO_KIND_YEAR_CARD_NUMBER){
                return String.format(" 此年包一月内已兑换%s次，请留意是否存在异常行为", tiemoCardUserLogs.size());
            }
        }
        //diy年包处理 （只处理退款逻辑）
        if(SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode().equals(smallpro.getKind())){
            List<DiyTimeCardPo> diyTimeCardList = SpringUtil.getBean(DiyTimeCardMapper.class).selectLogByBasketId(smallproBill.getBasketId(), startTime, endTime);
            if(diyTimeCardList.size()>=SMALL_PRO_KIND_YEAR_CARD_NUMBER){
                return String.format(" 此年包一月内已兑换%s次，请留意是否存在异常行为", diyTimeCardList.size());
            }
        }
        //3.质保换新使用总次数超过3次及以上（本次接件不算）
        //判断商品是否壳膜
        Boolean isShellAndFilm = categoryService.determineShellAndFilm(smallpro);
        if (SmallProServiceTypeEnum.SMALL_PRO_KIND_WARRAN_TYRE_PLACEMENT.equals(smallpro.getServiceType())
                && SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())
                && CollectionUtils.isNotEmpty(smallproBillList) && smallproBillList.size() == 1
                && isShellAndFilm) {
            List<Smallpro> smallproList = this.baseMapper.selectLogByBasketId(smallproBill.getBasketId());
            if(smallproList.size()>=NumberConstant.THREE){
                return String.format("该订单质保换新已使用%s次，请留意是否存在异常行为", smallproList.size());
            }
        }
        // 质保期间内换货异常
        if(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())){
            Integer basketId = smallproBill.getBasketId();
            List<Integer> basketIdList = getrecursiveBasketList(Collections.singletonList(basketId));
            basketIdList.add(basketId);
            //递归查询basketId
            List<RecursiveQueriesReq> recursiveQueriesReqs = this.baseMapper.selectRecursiveQueriesReq(basketId);
            if(CollectionUtils.isNotEmpty(recursiveQueriesReqs)){
                basketIdList.addAll(Optional.of(recursiveQueriesReqs.stream().filter(item -> Objects.nonNull(item.getNewBasketId()))
                        .map(RecursiveQueriesReq::getNewBasketId)
                        .collect(Collectors.toList())).orElse(new ArrayList<>()));
            }
            //3.特殊统计 使用特殊质保进行换货次数在2次及以上（本次接件不算）
            List<Smallpro> smallproSpecialList = this.baseMapper.selectSpecialExchanges(basketIdList,NumberConstant.ONE);
            if(smallproSpecialList.size()>=NumberConstant.TWO){
                return String.format("该订单已特殊质保换货%s次，请留意是否存在异常行为", smallproSpecialList.size());
            }
            //4.正常统计 不使用特殊质保换货总数量超过3次及以上 （本次接件不算）且没有使用年包和质保换新服务
            List<Smallpro> smallproList = this.baseMapper.selectSpecialExchanges(basketIdList,NumberConstant.ZERO);
            smallproList.removeIf(item->{
                return SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode().equals(item.getServiceType())
                        || SmallProServiceTypeEnum.SMALL_PRO_KIND_WARRAN_TYRE_PLACEMENT.getCode().equals(item.getServiceType());
            });
            if(smallproList.size()>=NumberConstant.THREE){
                return String.format("该订单已保换货%s次，请留意是否存在异常行为", smallproList.size());
            }
        }
        //5.用户频繁退货异常 同一个用户退货次数在3次及以上
        if(SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode().equals(smallpro.getKind())){
            Integer userId = Optional.ofNullable(smallpro.getUserId()).orElseThrow(() -> new CustomizeException("用户id获取为空"));
            //获取该用户退货次数
            List<Smallpro> list = this.lambdaQuery().eq(Smallpro::getUserId, userId)
                    .ne(Smallpro::getStats, SmallProStatsEnum.SMALL_PRO_STATS_DELETED.getCode())
                    .list();
            if(list.size()>=NumberConstant.THREE){
                return String.format("该客户已小件退款操作%s次，请留意是否存在异常行为。", list.size());
            }
        }

        return "";
    }

    /**
     * 获取basketId
     * @param basketIds
     * @return
     */
    private List<Integer> getrecursiveBasketList(List<Integer> basketIds){
        List<Integer> basketIdList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(basketIds)){
            for (Integer basketId : basketIds){
                basketIdList.add(basketId);
                List<RecursiveQueriesReq> recursiveQueriesReqs = this.baseMapper.selectRecursiveQueriesReq(basketId);
                if(CollectionUtils.isEmpty(recursiveQueriesReqs)){
                    continue;
                }
                //添加递归查询的basketId
                recursiveQueriesReqs.stream()
                        .map(RecursiveQueriesReq::getNewBasketId)
                        .filter(Objects::nonNull)
                        .forEach(basketIdList::add);
                //查找第一个basketId recursiveQueriesReqs 根据smallProId 进行排序
                Optional<RecursiveQueriesReq> first = recursiveQueriesReqs.stream().sorted(Comparator.nullsLast(Comparator.comparing(RecursiveQueriesReq::getSmallProId))).findFirst();
                first.ifPresent(item->{
                    List<SmallproBill> list = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, item.getSmallProId()).list();
                    if(CollectionUtils.isNotEmpty(list)){
                        SmallproBill smallproBill = list.get(0);
                        basketIdList.add(smallproBill.getBasketId());
                    }
                });
            }
        }
        return basketIdList;
    }

    @Override
    public List<HistoricalProcessingRes> selectHistoricalProcessing(HistoricalProcessingReq req) {
        List<Integer> basketIds = req.getBasketIds();
        List<Integer> basketIdList = getrecursiveBasketList(basketIds);
        req.setBasketIdList(basketIdList);
        List<HistoricalProcessingRes> historicalProcessingResAll = new ArrayList<>();
        //会员查询
        if(ObjectUtil.isNotNull(req.getUserId())){
            HistoricalProcessingReq reqNew = new HistoricalProcessingReq();
            BeanUtils.copyProperties(req,reqNew);
            reqNew.setBasketIdList(new ArrayList<>());
            reqNew.setInuer("");
            List<HistoricalProcessingRes> historicalProcessingRes = this.baseMapper.selectHistoricalProcessing(reqNew);
            historicalProcessingResAll.addAll(historicalProcessingRes);
        }
        //接件人查询
        if(StringUtils.isNotEmpty(req.getInuer())){
            HistoricalProcessingReq reqNew = new HistoricalProcessingReq();
            BeanUtils.copyProperties(req,reqNew);
            reqNew.setBasketIdList(new ArrayList<>());
            reqNew.setUserId(null);
            List<HistoricalProcessingRes> historicalProcessingRes = this.baseMapper.selectHistoricalProcessing(reqNew);
            historicalProcessingResAll.addAll(historicalProcessingRes);
        }
        //商品查询
        if(CollectionUtils.isNotEmpty(req.getBasketIds())){
            HistoricalProcessingReq reqNew = new HistoricalProcessingReq();
            BeanUtils.copyProperties(req,reqNew);
            reqNew.setUserId(null);
            reqNew.setInuer("");
            List<HistoricalProcessingRes> historicalProcessingRes = this.baseMapper.selectHistoricalProcessing(reqNew);
            historicalProcessingResAll.addAll(historicalProcessingRes);
        }
        historicalProcessingResAll = historicalProcessingResAll.stream().distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(historicalProcessingResAll)){
            historicalProcessingResAll.subList(NumberConstant.ZERO, Math.min(historicalProcessingResAll.size(), NumberConstant.TEN));
            //收集historicalProcessingRes里面的changePpriceid和ppriceid
            List<Integer> ppidList = new ArrayList<>();
            List<Integer> changePpriceidList = historicalProcessingResAll.stream().map(HistoricalProcessingRes::getChangePpriceid).collect(Collectors.toList());
            ppidList.addAll(changePpriceidList);
            List<Integer> ppriceidList = historicalProcessingResAll.stream().map(HistoricalProcessingRes::getPpriceid).collect(Collectors.toList());
            ppidList.addAll(ppriceidList);
            Map<Integer, Productinfo> productMap = productinfoService.getProductMapByPpids(ppidList);
            //参数封装
            historicalProcessingResAll.forEach(item->{
                //处理方式设置
                item.setKindValue(SmallProKindEnum.getMessageByCode(item.getKind()));
                //状态设置
                item.setStatsValue(SmallProStatsEnum.getMessageByCode(item.getStats()));
                //服务名称设置
                item.setServiceTypeValue(SmallProServiceTypeEnum.getMessageByCode(item.getServiceType()));
                //商品名称设置
                Productinfo productinfo = productMap.getOrDefault(item.getPpriceid(), new Productinfo());
                item.setPpriceidName(Optional.ofNullable(productinfo.getProductName()).orElse("")+productinfo.getProductColor());
                Productinfo productInfoChange = productMap.getOrDefault(item.getChangePpriceid(), new Productinfo());
                item.setChangePpriceidName(Optional.ofNullable(productInfoChange.getProductName()).orElse("")+productInfoChange.getProductColor());
            });
        }
        return historicalProcessingResAll;
    }

    /**
     *  存在以下情况那就不能走组合退
     * （1）同一个小件单接件多个商品
     * （2）出险年包服务
     * （3）相同ppid商品换货
     * （4）大件附件换货
     * @param id
     * @return
     */
    @Override
    public boolean isHqtxh(Integer id) {
        return SpringContextUtil.reqCache(() -> {
            Smallpro smallpro = Optional.ofNullable(smallproService.getByIdSqlServer(id)).orElseThrow(() -> new CustomizeException("小件单查询为空"));
            return isHqtxh(smallpro);
        }, RequestCacheKeys.SMALLPRO_SERVICE_IS_HQTXH, id);
    }

    /**
     * 返回false 那就是要不生成销售单 走原始逻辑
     *   返回true 那就是生成销售单
     * @param smallpro
     * @return
     */
    @Override
    public boolean isHqtxh(Smallpro smallpro) {
        //只有九机才开始走这个逻辑
        if(XtenantEnum.isSaasXtenant()){
            return false;
        }
        if (openAreaId(smallpro.getAreaId())){
            return false;
        }
        //判断如果过不是换货 那就直接返回false
        if(!SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())){
            return false;
        }
        //多个接件标记
        List<SmallproBill> smallproBills;
        if(smallpro instanceof SmallproReq){
            smallproBills = ((SmallproReq) smallpro).getSmallproBillList();
        } else {
            smallproBills = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallpro.getId()).list();
        }
        if(CollectionUtils.isEmpty(smallproBills)){
            throw new CustomizeException("接件商品不能为空");
        }
        List<ConnectorProductDetail> connectorProductDetails = smallproBills.stream().map(item -> {
            ConnectorProductDetail connectorProductDetail = new ConnectorProductDetail();
            connectorProductDetail.setConnectorPpid(item.getPpriceid().intValue())
                    .setMobileExchangeFlag(item.getMobileExchangeFlag())
                    .setBasketId(item.getBasketId())
                    .setConnectorPpid(Convert.toInt(item.getPpriceid()))
                    .setConnectorPpidCount(item.getCount());

            return connectorProductDetail;
        }).collect(Collectors.toList());
        // 出险年包服务
        Integer serviceType = smallpro.getServiceType();
        //相同ppid商品换货
        Integer changePpriceid = Optional.ofNullable(smallpro.getChangePpriceid()).orElse(Integer.MIN_VALUE);
        HqtxhReq hqtxhReq = new HqtxhReq();
        hqtxhReq.setConnectorProductDetailList(connectorProductDetails)
                .setServiceType(serviceType)
                .setChangePpid(changePpriceid)
                .setSubId(smallpro.getSubId())
                .setSmallproAreaId(smallpro.getAreaId())
                .setSmallProId(smallpro.getId())
                .setKind(smallpro.getKind());
        HqtxhRes hqtxhRes = generateSalesOrder(hqtxhReq);
        return hqtxhRes.getGenerateSalesOrder();
    }

    /**
     * apollo控制门店
     * @return
     */
    private boolean openAreaId(Integer smallproAreaId) {
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElse(new OaUserBO());
        List<String> hqtxhAreaId = Arrays.stream(apollo.getHqtxhAreaId().split(",")).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
        Integer areaId = Optional.ofNullable(smallproAreaId).orElse(oaUserBO.getAreaId());
        return CollectionUtils.isNotEmpty(hqtxhAreaId) && !hqtxhAreaId.contains(areaId.toString());
    }


    // region 删除小件接件单 delSmallpro
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SmallproNormalCodeMessageRes delSmallpro(Integer id) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();

        //增加 判断小件已收银不允许删除
        Smallpro smallpro = getById(id);
        if (!Boolean.TRUE.equals(smallpro.getIsTui())) {
            Boolean isCashRegisterLock = smallpro.getIsShouyingLock();
            if (Boolean.TRUE.equals(isCashRegisterLock)) {
                result.setCode(THRESHOLD_VALUE);
                result.setMessage("已收银未退款不允许删除！");
                return result;
            }

            Integer count = shouhouTuihuanService.count(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getCheck3, NumberConstant.ONE)
                    .eq(ShouhouTuihuan::getSmallproid, id)
                    .in(ShouhouTuihuan::getTuihuanKind, Arrays.asList(TuihuanKindEnum.TPJ.getCode(),TuihuanKindEnum.SMALL_PRO_REFUND.getCode()
                            ,TuihuanKindEnum.SMALL_PRO_HQTXH.getCode(), TuihuanKindEnum.SMALL_PRO_REFUND_REPAIR_FEE.getCode()))
                    .or(bo -> bo.eq(ShouhouTuihuan::getShouhouId, id).in(ShouhouTuihuan::getTuihuanKind,
                            Arrays.asList(TuihuanKindEnum.TPJ.getCode(), TuihuanKindEnum.SMALL_PRO_REFUND.getCode(), TuihuanKindEnum.SMALL_PRO_REFUND_REPAIR_FEE.getCode())))
                    .and(bo -> bo.eq(ShouhouTuihuan::getIsdel, NumberConstant.ZERO).or().isNull(ShouhouTuihuan::getIsdel))
            );
            Optional.ofNullable(count).filter(CommenUtil::isNotNullZero)
                    .ifPresent(k -> {
                        throw new CustomizeException("未退款且存在办理完成的退换单，不可以删除");
                    });
        }
        // 要先取消备货才能删除小件单
        if(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())
            && smallproBillService.existLockStock(smallpro.getId(), null)){
            throw new CustomizeException("换货商品已备货，请先取消备货");
        }

        UpdateWrapper<Smallpro> deleteSmallproWrapper = new UpdateWrapper<>();
        deleteSmallproWrapper.lambda().set(Smallpro::getIsDel, 1).set(Smallpro::getStats, NumberConstant.TWO)
                .eq(Smallpro::getId, id).apply("isnull(isdel,0)=0").ne(Smallpro::getStats, 1).ne(Smallpro::getStats, NumberConstant.THREE);
        boolean updateFlag = ((SmallproServiceImpl) AopContext.currentProxy()).update(deleteSmallproWrapper);
        OaUserBO oaUserBo = currentRequestComponent.getCurrentStaffId();
        if (updateFlag) {
            LambdaQueryWrapper<ShouhouTuihuan> deleteAfterSaleREWrapper = new LambdaQueryWrapper<>();
            deleteAfterSaleREWrapper
                    .isNull(ShouhouTuihuan::getCheck3)
                    .and(wrapper -> wrapper.eq(ShouhouTuihuan::getIsdel, 0).or().isNull(ShouhouTuihuan::getIsdel))
                    .eq(ShouhouTuihuan::getSmallproid, id)
                    .or(bo -> bo.eq(ShouhouTuihuan::getShouhouId, id)
                            .in(ShouhouTuihuan::getTuihuanKind, Arrays.asList(TuihuanKindEnum.SMALL_PRO_REFUND.getCode(), TuihuanKindEnum.SMALL_PRO_REFUND_REPAIR_FEE.getCode())))
            ;
            SmallproRefundExService smallproRefundExService = SpringUtil.getBean(SmallproRefundExService.class);
            ShouhouTuihuan shouhouTuihuan = CollUtil.get(shouhouTuihuanService.listSqlServer(deleteAfterSaleREWrapper), 0);
            boolean afterSaleREFlag = false;
            if (shouhouTuihuan != null) {
                if(ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode().equals(shouhouTuihuan.getTuiKinds())){
                    R<Boolean> cancelGroupResult = SpringUtil.getBean(RefundMoneyService.class).cancelRefund(shouhouTuihuan.getId());
                    if(!cancelGroupResult.isSuccess()){
                        throw new CustomizeException(cancelGroupResult);
                    }
                }else{
                    shouhouTuihuan.setIsdel(true).setDelUser(oaUserBo.getUserName()).setDelTime(LocalDateTime.now());
                    afterSaleREFlag = smallproRefundExService.cancelRefundCheckWrite(new SmallproNormalCodeMessageRes(), shouhouTuihuan);
                }
            }
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append("删除小件单成功！");
            if (afterSaleREFlag) {
                messageBuilder.append("删除小件单对应的售后退换单成功！");
            }
            result.setCode(0);
            result.setMessage(messageBuilder.toString());
            //添加日志
            smallproLogService.addLogs(id, "删除小件单成功！", null,
                    oaUserBo.getUserName(), 0);
            //清除小件不折价缓存
            //获取小件订单关联basketId
            QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
            smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, id);
            List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
            if (CollUtil.isNotEmpty(smallproBillList)) {
                for (SmallproBill smallproBill : smallproBillList) {
                    //小件无折扣退款
                    stringRedisTemplate.delete(StrUtil.format(RedisKeys.SMALLPRO_NOT_DISCOUNT_BASKET_KEY, smallproBill.getBasketId()));
                }
            }
        } else {
            result.setCode(THRESHOLD_VALUE);
            result.setMessage("删除小件单失败！数据库更新失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return result;
    }
    // endregion

    // region 获取小件接件查询筛选列表 getSmallproQueryCondition

    @Override
    public SmallproQueryConditionRes getSmallproQueryCondition() {
        // 关键字类别
        HashMap<String, Object> keyKind = new HashMap<>(INIT_LEN);
        HashMap<String, Object> oldKeyKind = new HashMap<>(INIT_LEN);
        SmallProQueryConditionType[] keyKindEnumArray
                = SmallProQueryConditionType.class.getEnumConstants();
        keyKind.put("key", "keyKind");
        oldKeyKind.put("key", "keyKind");
        List<HashMap<String, Object>> keyKindOptionList = new ArrayList<>(keyKindEnumArray.length);
        List<HashMap<String, Object>> oldKeyKindOptionList = new ArrayList<>(keyKindEnumArray.length);
        HashMap<String, Object> temp;
        for (SmallProQueryConditionType keyKindTemp : keyKindEnumArray) {
            if (keyKindTemp.getKey() != null) {
                temp = new HashMap<>(INIT_LEN);
                temp.put("label", keyKindTemp.getMessage());
                temp.put("value", keyKindTemp.getKey());
                switch (keyKindTemp.getType()) {
                    case 0:
                        oldKeyKindOptionList.add(temp);
                        break;
                    case 1:
                        keyKindOptionList.add(temp);
                        break;
                    default:
                        break;
                }
            }
        }
        keyKind.put("option", keyKindOptionList);
        oldKeyKind.put("option", oldKeyKindOptionList);
        // 通常状态
        HashMap<String, Object> status = new HashMap<>(INIT_LEN);
        buildUpOption(SmallProStatsEnum.class, "status", status);
        // 换货状态
        HashMap<String, Object> exchangeStatus = new HashMap<>(INIT_LEN);
        buildUpOption(SmallProStatsExchangeEnum.class, "exchangeCheckState", exchangeStatus);
        // 退货状态
        HashMap<String, Object> returnStatus = new HashMap<>(INIT_LEN);
        buildUpOption(SmallProStatsReturnEnum.class, "returnCheckState", returnStatus);
        // 筛选时间类别
        HashMap<String, Object> timeKind = new HashMap<>(INIT_LEN);
        buildUpOption(SmallproTimeTypeEnum.class, "timeKind", timeKind);

        HashMap<String, Object> checkStatus = new HashMap<>();
        buildUpOption(SmallProCheckStatusEnum.class, "checkStatus", checkStatus);

        // 筛选时间类别
        HashMap<String, Object> timeType = new HashMap<>(INIT_LEN);
        buildUpOption(SmallproOldTimeTypeEnum.class, "timeType", timeType);
        // 筛选发货异常类别
        HashMap<String, Object> abnormalType = new HashMap<>(INIT_LEN);
        buildUpOption(AbnormalTypeEnum.class, "abnormalType", abnormalType);
        // 处理方式
        SmallProKindEnum[] kindEnumArray = SmallProKindEnum.class.getEnumConstants();
        HashMap<String, Object> kind = new HashMap<>(INIT_LEN);
        HashMap<String, Object> oldKind = new HashMap<>(INIT_LEN);
        List<HashMap<String, Object>> kindOptionList = new ArrayList<>(kindEnumArray.length);
        List<HashMap<String, Object>> oldKindOptionList = new ArrayList<>(kindEnumArray.length);
        kind.put("key", "kind");
        oldKind.put("key", "kind");
        for (SmallProKindEnum kindTemp : kindEnumArray) {
            if (kindTemp.getCode() != null) {
                temp = new HashMap<>(INIT_LEN);
                temp.put("label", kindTemp.getMessage());
                temp.put("value", kindTemp.getCode());
                oldKindOptionList.add(temp);
                // 管理列表需要关联状态
                HashMap<String, Object> kindStats = new HashMap<>(2);
                kindStats.put("message", kindTemp.getMessage());
                switch (kindTemp.getCode()) {
                    case 2:
                        temp.put("children", exchangeStatus);
                        break;
                    case 3:
                        temp.put("children", returnStatus);
                        break;
                    default:
                        break;
                }
                kindOptionList.add(temp);
            }
        }
        kind.put("option", kindOptionList);
        oldKind.put("option", oldKindOptionList);
        // 转地区状态
        HashMap<String, Object> toAreaStatus = new HashMap<>(INIT_LEN);
        buildUpOption(SmallProToAreaStatsEnum.class, "toAreaStatus", toAreaStatus);
        // 退还旧件状态
        HashMap<String, Object> oldStatus = new HashMap<>(INIT_LEN);

        oldStatus.put("key", "status");
        temp = new HashMap<>(2);
        temp.put("label", "待处理");
        temp.put("value", SmallProStatsEnum.SMALL_PRO_STATS_PROCESSING.getCode());
        List<HashMap<String, Object>> oldStatsOptionList = new ArrayList<>();
        oldStatsOptionList.add(temp);
        temp = new HashMap<>(2);
        temp.put("label", "已处理");
        temp.put("value", SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode());
        List<HashMap> oldExtendStatsList = new ArrayList<>(INIT_LEN);
        HashMap oldExtendStats = new HashMap(INIT_LEN);
        oldExtendStats.put("label", SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getMessage());
        oldExtendStats.put("value", SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode());
        oldExtendStatsList.add(oldExtendStats);
        oldExtendStats = new HashMap(INIT_LEN);
        oldExtendStats.put("label", SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_INTO_STOCK.getMessage());
        oldExtendStats.put("value", SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_INTO_STOCK.getCode());
        oldExtendStatsList.add(oldExtendStats);
        oldExtendStats = new HashMap(INIT_LEN);
        oldExtendStats.put("label", SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_SCRAPPED.getMessage());
        oldExtendStats.put("value", SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_SCRAPPED.getCode());
        oldExtendStatsList.add(oldExtendStats);
        HashMap oldExtendStatsTemp = new HashMap(INIT_LEN);
        oldExtendStatsTemp.put("key", "status2");
        oldExtendStatsTemp.put("option", oldExtendStatsList);
        temp.put("children", oldExtendStatsTemp);
        oldStatsOptionList.add(temp);
        oldStatus.put("option", oldStatsOptionList);
        // 退换旧件返厂状态
        HashMap returnToFactoryStatus = new HashMap(ShouhouFanchangRstatsEnum.values().length);
        buildUpOption(ShouhouFanchangRstatsEnum.class, "returnToFactoryStatus", returnToFactoryStatus);

        // 小件处理服务方式
        HashMap serviceType = new HashMap(SmallProServiceTypeEnum.values().length);
        buildUpOption(SmallProServiceTypeEnum.class, "serviceType", serviceType);

        // 通常状态
        SmallproQueryConditionRes result = new SmallproQueryConditionRes(keyKind, kind, status, toAreaStatus,
                oldStatus, oldKind, oldKeyKind, returnToFactoryStatus, timeKind, timeType, checkStatus, serviceType, abnormalType);
        return result;
    }

    // endregion

    // region 获取小件接件详情页面选择列表 getSmallproInfoSelection

    @Override
    public SmallproInfoSelectionRes getSmallproInfoSelection() {
        SmallproInfoSelectionRes smallproInfoSelectionRes = new SmallproInfoSelectionRes();
        // 保修状态
        HashMap<String, Object> warranty = new HashMap<>(INIT_LEN);
        buildUpOption(SmallproWarrantyStatusEnum.class, "warrantyStatus", warranty);
        // 通常状态
        HashMap<String, Object> status = new HashMap<>(INIT_LEN);
        buildUpOption(SmallProStatsEnum.class, "status", status);
        // 九机服务
        HashMap<String, Object> serviceType = new HashMap<>(INIT_LEN);
        buildUpOption(SmallProServiceTypeEnum.class, "serviceType", serviceType);
        //todo 这里从数据库获取 serviceType
//        sysConfigService.getValueByCode()

        // 处理方式
        HashMap<String, Object> kind = new HashMap<>(INIT_LEN);
        buildUpOption(SmallProKindEnum.class, "kind", kind);
        // 处理分组
        HashMap<String, Object> group = new HashMap<>(INIT_LEN);
        buildUpOption(SmallGroupEnum.class, "group", group);
        // 数据绑定
        HashMap<String, Object> dataRelease = new HashMap<>(INIT_LEN);
        buildUpOption(SmallproDataReleaseEnum.class, "dataRelease", dataRelease);
        // 外观情况
        HashMap<String, Object> outwardFlag = new HashMap<>(INIT_LEN);
        buildUpOption(SmallproOutwardFlagEnum.class, "outwardFlag", outwardFlag);
        // 情况选项
        HashMap<Integer, HashMap<String, Object>> flagMap = new HashMap<>(INIT_LEN);
        HashMap<Integer, String> flagKeyMap = new HashMap<>(INIT_LEN);
        HashMap<String, Object> before = new HashMap<>(INIT_LEN);
        HashMap<String, Object> after = new HashMap<>(INIT_LEN);
        flagMap.put(0, before);
        flagKeyMap.put(0, "situationKindBefore");
        flagMap.put(1, after);
        flagKeyMap.put(1, "situationKindAfter");
        buildUpOptionByFlag(SmallproSituationKindEnum.class, flagKeyMap, flagMap);
        // 退换旧件返厂状态
        HashMap returnToFactoryStatus = new HashMap(INIT_LEN);
        buildUpOption(ShouhouFanchangRstatsEnum.class, "returnToFactoryStatus", returnToFactoryStatus);
        // 维修状态
        HashMap maintainState = new HashMap(INIT_LEN);
        buildUpOption(SmallproMaintainStateEnum.class, "maintainState", maintainState);
        //收款状态
        HashMap collectionStatus = new HashMap<>(INIT_LEN);
        buildUpOption(CollectionStatusEnum.class, "collectionStatus", collectionStatus);

        smallproInfoSelectionRes.setDataReleaseSelection(dataRelease)
                .setGroupSelection(group)
                .setKindSelection(kind)
                .setOutwardFlagSelection(outwardFlag)
                .setServiceTypeSelection(serviceType)
                .setWarrantyStatusSelection(warranty)
                .setSituationKindBeforeSelection(before)
                .setSituationKindAfterSelection(after)
                .setReturnToFactoryStatus(returnToFactoryStatus)
                .setMaintainState(maintainState)
                .setCollectionStatus(collectionStatus)
                .setStatus(status);
        return smallproInfoSelectionRes;
    }

    // endregion

    // region 获取小件接件单列表 getSmallproPage


    /**
     * basketId 转换成subid
     * @param query
     */
    private void basketIdConvertSubIdList(SmallproReturnGoodsReq query){
        try {
            String keyKind = query.getKeyKind();
            String key = query.getKey();
            //如果过是basketId那才进行转换
            if(SmallProQueryConditionType.QUERY_CONDITION_LIST_BASKET_ID.getKey().equals(keyKind) && StringUtils.isNumeric(key)){
                List<Integer> basketIdList = Collections.singletonList(Integer.valueOf(key));
                //获取到相关basketId
                List<Integer> aboutBasketIdList = SpringUtil.getBean(SmallproDetailsExService.class).selectAboutBasketId(basketIdList, new HashMap<>());
                if(CollectionUtils.isNotEmpty(aboutBasketIdList)){
                    // 使用流API查询SmallproBill实体中与提供的basketId列表匹配的smallproID列表
                    List<Integer> smallProIds = smallproBillService.lambdaQuery().in(SmallproBill::getBasketId, aboutBasketIdList)
                            .list().stream()
                            .flatMap(smallproBill -> Stream.of(smallproBill.getSmallproID()))
                            .collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(smallProIds)){
                        query.setSmallProIds(smallProIds);
                    } else{
                        query.setSmallProIds(Collections.singletonList(-1));
                    }
                } else{
                    query.setSmallProIds(Collections.singletonList(-1));
                }
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("小件接件列表查询basketId的情况", Dict.create().set("query", JSONUtil.toJsonStr(query)),e,smsService::sendOaMsgTo9JiMan);
        }
    }
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<PageVo> getSmallproPage(SmallproReturnGoodsReq query, Integer pageSize, Integer currentPage, boolean isWithOperateLog) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (currentPage == null || currentPage == 0) {
            currentPage = 1;
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 1;
        }
        //basketId 转换成subid
        basketIdConvertSubIdList(query);
        PageVo result = new PageVo(currentPage, pageSize);
        R<PageVo> r = R.success(result);
        List<String> keyKinds = Stream.of("id", "subId", "mobile", "ppid").collect(Collectors.toList());
        if (StringUtils.isNotEmpty(query.getKeyKind()) && keyKinds.contains(query.getKeyKind())) {
            if (StringUtils.isNotEmpty(query.getKey()) && !StringUtils.isNumeric(query.getKey())) {
                return r;
            }
        }
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        if (authPart) {
            AreaBelongsDcHqD1AreaId backInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
            //非HQ地区，数据需要隔离,排除九机
            if (backInfo != null && oaUserBO.getXTenant() >= XTENANT && !CollUtil.contains(backInfo.getHqAreaIds(), oaUserBO.getAreaId())) {
                query.setIsAuthPart(Boolean.TRUE);
                query.setAuthorizeId(oaUserBO.getAuthorizeId());
            }
        }
        Integer total = baseMapper.getSmallproReturnGoodsNum(query);
        if (total == 0) {
            return r;
        }
        Integer startRow = pageSize * (currentPage - 1);
        List<SmallproReturnGoodsRes> list = baseMapper.getSmallproReturnGoodsList(query, pageSize, startRow);
        List<String> inUserNameList = list.stream().map(SmallproReturnGoodsRes::getInUser).distinct().collect(Collectors.toList());
        List<List<String>> listSplit = CommenUtil.getOverInList(inUserNameList);
        List<SmallproInfoInuserInfoBO> inUserInfoBOList = new ArrayList<>();
        for (List<String> strings : listSplit) {
            inUserInfoBOList.addAll(baseMapper.getSmallproInUserInfo(strings));
        }
        Map<Integer, SmallproInfoInuserInfoBO> inUserIdInUserMap =
                inUserInfoBOList.stream().collect(Collectors.toMap(SmallproInfoInuserInfoBO::getInUserId,
                        bo -> bo));
        Map<String, Integer> inUserNameInUserIdMap = new HashMap<>(inUserInfoBOList.size());
        inUserInfoBOList.forEach(bo -> {
            Integer inUserId = inUserNameInUserIdMap.get(bo.getInUserName());
            if (inUserId != null) {
                SmallproInfoInuserInfoBO temp = inUserIdInUserMap.get(inUserId);
                if (temp != null) {
                    inUserId = bo.getIsZaiZhi() ? bo.getInUserId() : (temp.getIsZaiZhi() ?
                            (bo.getInUserId() > temp.getInUserId() ? bo.getInUserId() : temp.getInUserId()) :
                            temp.getInUserId());
                    inUserNameInUserIdMap.put(bo.getInUserName(), inUserId);
                } else {
                    inUserNameInUserIdMap.put(bo.getInUserName(), bo.getInUserId());
                }
            } else {
                inUserNameInUserIdMap.put(bo.getInUserName(), bo.getInUserId());
            }
        });
        if (CollectionUtils.isNotEmpty(list)) {
            //获取所有小件单id
            List<Integer> smallProIdList = list.stream().map(SmallproReturnGoodsRes::getId).collect(Collectors.toList());
            //获取SmallproBill相关信息
            Map<Integer, SmallproBill> smallproBillMap = CommonUtils.bigDataInQuery(smallProIdList, ids -> smallproBillService.lambdaQuery().in(SmallproBill::getSmallproID, ids).list())
                    .stream().collect(Collectors.toMap(SmallproBill::getSmallproID, Function.identity(), (n1, n2) -> n2));
            //串号大图获取
            Map<Integer, List<Attachments>> attachmentMap = CommonUtils.bigDataInQuery(smallProIdList,
                            ids -> attachmentsService.listAttachments(AttachmentsEnum.BIG_IMEI_ATTACHMENTS.getCode(),CollUtil.newHashSet(ids),null))
                    .stream().collect(Collectors.groupingBy(Attachments::getLinkedID));

            String data = sysConfigClient.getValueByCode(SysConfigConstant.IMG_URL).getData();
            list = list.stream().map(e -> {
                if (SmallproGroupEnum.MOBILE_PHONE_PARTS.getGroupId().equals(e.getGroupId())) {
                    e.setGroupName(SmallproGroupEnum.MOBILE_PHONE_PARTS.getName());
                } else if (SmallproGroupEnum.SMALL_APPLIANCES.getGroupId().equals(e.getGroupId())) {
                    e.setGroupName(SmallproGroupEnum.SMALL_APPLIANCES.getName());
                } else if (SmallproGroupEnum.DIGITAL_COMPUTER.getGroupId().equals(e.getGroupId())) {
                    e.setGroupName(SmallproGroupEnum.DIGITAL_COMPUTER.getName());
                }
                e.setPpriceid(Optional.ofNullable(smallproBillMap.getOrDefault(e.getId(), new SmallproBill()).getPpriceid()).orElse(0L).intValue());
                e.setServiceTypeValue(SmallProServiceTypeEnum.getMessageByCode(e.getServiceType()));
                e.setInUserId(inUserNameInUserIdMap.get(e.getInUser()));
                // 设置接件时间 未取机：接件时间-当前时间 已取机：接件时间-取机时间
                setInDate(e);
                if (isWithOperateLog) {
                    List<SmallproOperationInfoBO> operationInfoList = getSmallproOperationLogs(e.getId(), 0);
                    e.setOperationInfoList(operationInfoList);
                    setFlowDate(e, operationInfoList);
                }
                String imageLinkSmall = (StringUtils.isEmpty(e.getFid())) ? "" : StrUtil.indexedFormat("{0}/newstatic/{1}", data, e.getFid());
                e.setImageLink(imageLinkSmall);
                ImeiImageInfo imeiImageInfo = new ImeiImageInfo();
                imeiImageInfo.setImageLinkSmall(imageLinkSmall);
                List<Attachments> attachments = attachmentMap.get(e.getId());
                if(CollectionUtils.isNotEmpty(attachments)){
                    String fid = Optional.ofNullable(attachments.get(NumberConstant.ZERO)).orElse(new Attachments()).getFid();
                    imeiImageInfo.setImageLinkBig(StrUtil.indexedFormat("{0}/newstatic/{1}", data, fid));
                }
                e.setImeiImageInfo(imeiImageInfo);
                return e;
            }).collect(Collectors.toList());
        }
        //计算本页合计与总合计
        Map<String, Object> exData = new HashMap<>(NumberConstant.ONE);
        SmallproInfoCostCalculateRes smallproInfoCostCalculateRes = new SmallproInfoCostCalculateRes();
        //计算当前页合计
        SmallproInfoCostCalculateRes.Cost currentPageCost = new SmallproInfoCostCalculateRes.Cost();
        AtomicReference<BigDecimal> costPrice = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> maintainPrice = new AtomicReference<>(BigDecimal.ZERO);
        list.forEach(l -> {
            costPrice.set(costPrice.get().add(Optional.ofNullable(l.getCostPrice()).orElse(BigDecimal.ZERO)).setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
            maintainPrice.set(maintainPrice.get().add(Optional.ofNullable(l.getMaintainPrice()).orElse(BigDecimal.ZERO)).setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
            currentPageCost.setCostPrice(costPrice.get());
            currentPageCost.setMaintainPrice(maintainPrice.get());
        });

        //计算总和计
        SmallproInfoCostCalculateRes.Cost assessCost = baseMapper.getSmallproCurrentPageCost(query);

        smallproInfoCostCalculateRes.setAssessCost(assessCost);
        smallproInfoCostCalculateRes.setCurrentPageCost(currentPageCost);

        exData.put(TOTAL, smallproInfoCostCalculateRes);
        result.setRecords(list);
        result.setTotal(total);
        result.setPages(PageUtil.getPages(total, pageSize));
        r.setExData(exData);
        return r;
    }

    private void setFlowDate(SmallproReturnGoodsRes e, List<SmallproOperationInfoBO> operationInfoList) {
        if (CollectionUtils.isEmpty(operationInfoList)) {
            return;
        }
        SmallproOperationInfoBO smallproOperationInfoBO =
                operationInfoList.stream().filter(q -> q.getCommentType() != null && q.getCommentType().equals(1))
                        .sorted(Comparator.comparing(SmallproOperationInfoBO::getInDate).reversed()).findFirst().orElse(null);
        if (null == smallproOperationInfoBO) {
            return;
        }
        LocalDateTime inDate = smallproOperationInfoBO.getInDate();
        LocalDateTime quJiAnDate = e.getQuJiAnDate();
        if (null == quJiAnDate) {
            quJiAnDate = LocalDateTime.now();
        }
        String comment = getDateBetween(quJiAnDate, inDate);
        e.setFollowUpTime(comment);
    }

    private void setInDate(SmallproReturnGoodsRes e) {
        String inDate = e.getInDate();
        LocalDateTime quJiAnDate = e.getQuJiAnDate();
        if (StringUtils.isEmpty(inDate)) {
            return;
        }
        LocalDateTime inDateLocal = DateUtil.stringToLocalDateTime(inDate);
        if (null == quJiAnDate) {
            quJiAnDate = LocalDateTime.now();
        }
        String comment = getDateBetween(quJiAnDate, inDateLocal);
        e.setInDate(comment);
    }

    private String getDateBetween(LocalDateTime quJiAnDate, LocalDateTime inDateLocal) {
        // 显示逻辑：＜1小时显示分钟，＜1天显示小时，＞24小时显示天（多出24小时一秒的算两天）。
        String comment = "";
        Duration duration = Duration.between(inDateLocal, quJiAnDate);
        if (duration.toMinutes() < 60) {
            comment = DateUtil.localDateTimeToString(inDateLocal) + "(" + duration.toMinutes() + "分钟)";
        } else if (duration.toHours() < 24) {
            comment = DateUtil.localDateTimeToString(inDateLocal) + "(" + duration.toHours() + "小时)";
        } else {
            long days = duration.toDays();
            long l = duration.toMillis();
            if (l > 0L) {
                days += 1;
            }
            comment = DateUtil.localDateTimeToString(inDateLocal) + "(" + days + "天)";
        }
        return comment;
    }

    // endregion

    // region 获取小件接件日志列表 getSmallproOperationLogs

    /**
     * description: <获取小件接件日志列表>
     * translation: <Get the list of small parts log>
     *
     * @param smallproId 小件接件单Id
     * @param showType   显示类别
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.SmallproOperationInfoBO>
     * <AUTHOR>
     * @date 11:09 2020/3/30
     * @since 1.0.0
     **/
    @Override
    public List<SmallproOperationInfoBO> getSmallproOperationLogs(Integer smallproId, Integer showType) {
        List<SmallproLogDocument> operationLogs = smallproLogService.findBySmallproId(smallproId);
        List<SmallproOperationInfoBO> result = new ArrayList<>(operationLogs.size());
        DateTimeFormatter operationDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        operationLogs.forEach(log -> {
            //处理日志中的url
            log.setComment(CommenUtil.handleLogMsgUrl(log.getComment()));
            SmallproOperationInfoBO smallproOperationInfoBO = new SmallproOperationInfoBO();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(log.getComment());
            stringBuilder.append("  【");
            stringBuilder.append(log.getInUser());
            stringBuilder.append("】");
            stringBuilder.append(log.getInDate().format(operationDateTimeFormatter));
            String information = stringBuilder.toString();
            smallproOperationInfoBO.setInformation(information).setShowFlag(log.getShowType()).setCommentType(log.getCommentType())
                    .setInDate(log.getInDate()).setComment(log.getComment()).setInUser(log.getInUser());
            if (showType.equals(1)) {
                if (log.getShowType() == 1) {
                    result.add(smallproOperationInfoBO);
                }
            } else {
                result.add(smallproOperationInfoBO);
            }
        });
        return result;
    }

    // endregion

    // region 添加小件接件进程日志 addSmallproLogWithPush

    @Override
    public SmallproLogRes addSmallproLogWithPush(SmallproAddLogReq smallproAddLogReq, String userName) {
        //phoneNum=18083850435&msg=XCXXXXXX&sendServer=9#{短信渠道}&sendTime=
        //sendto=#{userName}&title=#{标题}&msg=XXXXXX
        //测试消息测试消息( 内部邮件  短信 微信(APP) 已对接通知：#{InuserName})
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        StringBuilder stringBuilder = new StringBuilder("");
        StringBuilder comment = new StringBuilder("");
        comment.append(smallproAddLogReq.getComment());
        List<String> inUserNameList = new ArrayList<>();
        if (StrUtil.isNotBlank(smallproAddLogReq.getUserName())){
            inUserNameList.add(smallproAddLogReq.getUserName());
        }
        if(CollectionUtils.isNotEmpty(smallproAddLogReq.getUserNameList())){
            inUserNameList.addAll(smallproAddLogReq.getUserNameList());
        }
        if ((smallproAddLogReq.getToEmail().equals(1)
                || smallproAddLogReq.getToSms().equals(1)
                || smallproAddLogReq.getToWeixin().equals(1)) && CollUtil.isNotEmpty(inUserNameList)) {
            comment.append("( ");
            List<SmallproInfoInuserInfoBO> smallproInfoInuserInfoBOList =
                    baseMapper.getSmallproInUserInfo(inUserNameList);
            if (smallproInfoInuserInfoBOList.get(0).getInUserId() != null && smallproInfoInuserInfoBOList.get(0).getInUserId() > 0) {
                // 构建Message
                stringBuilder.append("小件接件： ");
                stringBuilder.append(smallproAddLogReq.getProductName().replace(",", "  "));
                stringBuilder.append(" , 接件单号：");
                stringBuilder.append(smallproAddLogReq.getSmallproId());
                stringBuilder.append(" ");
                stringBuilder.append(smallproAddLogReq.getComment());
                stringBuilder.append("[");
                stringBuilder.append(smallproAddLogReq.getUserName());
                stringBuilder.append("]");
                String message = stringBuilder.toString();
                // 是否发送邮件
                boolean sendEmail = smallproAddLogReq.getToEmail().equals(1);
                // 是否发送短信
                boolean sendSms = smallproAddLogReq.getToSms().equals(1);
                // 是否发送微信和APP
                boolean sendWx = smallproAddLogReq.getToWeixin().equals(1);
                long xtenant = Namespaces.get();
                String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
                if (XtenantEnum.isSaasXtenant()
                        && StringUtils.isNotEmpty(openXtenantStr)
                        && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                    R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(xtenant,
                            SmsConfigCodeEnum.CODE_97.getCode());
                    if (CommonUtils.isRequestSuccess(smsConfigResult)) {
                        SmsConfigVO smsConfig = smsConfigResult.getData();
                        // 消息内容
                        List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                        message = smsConfig.getTemplate();
                        if (StringUtils.isNotEmpty(message)
                                && org.apache.commons.collections4.CollectionUtils.isNotEmpty(fields)) {
                            for (SmsConfigVO.SmsField field : fields) {
                                if ("<productName>".equals(field.getValue())) {
                                    message = message.replace(field.getValue(), smallproAddLogReq.getProductName().replace(",", "  "));
                                }
                                if ("<subId>".equals(field.getValue())) {
                                    message = message.replace(field.getValue(), String.valueOf(smallproAddLogReq.getSmallproId()));
                                }
                                if ("<userName>".equals(field.getValue())) {
                                    message = message.replace(field.getValue(), smallproAddLogReq.getUserName());
                                }
                                if ("<comment>".equals(field.getValue())) {
                                    message = message.replace(field.getValue(), smallproAddLogReq.getComment());
                                }
                            }
                        }
                        // 推送方式
                        List<Integer> pushMethods = smsConfig.getPushMethod();
                        // 微信消息
                        sendWx = sendWx
                                && CollectionUtils.isNotEmpty(pushMethods)
                                && pushMethods.contains(SmsPushMethodEnum.WECHAT_MSG.getCode());
                        // sms消息
                        sendSms = sendSms
                                && CollectionUtils.isNotEmpty(pushMethods)
                                && pushMethods.contains(SmsPushMethodEnum.SMS.getCode());
                    }
                }
                // 内部邮件
                if (sendEmail) {
                    comment.append("内部邮件 ");
                    for (SmallproInfoInuserInfoBO userInfo:smallproInfoInuserInfoBOList){
                        HashMap<String, String> params = new HashMap<>(3);
                        params.put("sendto", userInfo.getInUserName());
                        params.put("title", "售后通知");
                        params.put("msg", message);
                        String url = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST) + SmallProRelativePathConstant.NOTICE_EMAIL;
                        String response = HttpClientUtil.post(url, params);
                    }

                }
                // 推送短信
                if (sendSms) {
                    comment.append("短信 ");
                    Integer smsChannelByTenant = smsService.getSmsChannelByTenant(oaUserBO.getAreaId(), ESmsChannelTypeEnum.YZMTD);
                    for (SmallproInfoInuserInfoBO userInfo:smallproInfoInuserInfoBOList){
                        // 获取发送渠道(验证码通道)
                        smsService.sendSms(userInfo.getMobile(), message, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统",smsChannelByTenant);
                    }

                }
                // 推送微信和App
                if (sendWx) {
                    comment.append("微信(APP) ");
                    for (SmallproInfoInuserInfoBO userInfo:smallproInfoInuserInfoBOList){
                        String url = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST) + String.format(SmallProRelativePathConstant.NOTICE_WEIXIN, userInfo.getInUserId(), message);
                        HttpClientUtil.get(url, new HashMap<>(0));
                    }
                }
                comment.append("已对接通知：");
                String userNames = smallproInfoInuserInfoBOList.stream().map(SmallproInfoInuserInfoBO::getInUserName).collect(Collectors.joining(","));
                comment.append(userNames);
                comment.append(")");
            }
        }
        String id = smallproLogService.addLogs(smallproAddLogReq.getSmallproId(), comment.toString(), null,
                userName, smallproAddLogReq.getShowType());
        SmallproLogDocument smallproLogDocument = smallproLogService.getSmallproLogById(id);
        StringBuilder restulBuilder = new StringBuilder("");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        restulBuilder.append(smallproLogDocument.getComment());
        restulBuilder.append(" ");
        restulBuilder.append("【");
        restulBuilder.append(smallproLogDocument.getInUser());
        restulBuilder.append("】");
        restulBuilder.append(smallproLogDocument.getInDate().format(dateTimeFormatter));
        SmallproLogRes smallproLogRes = new SmallproLogRes();
        smallproLogRes.setShowFlag(smallproLogDocument.getShowType()).setComment(smallproLogDocument.getComment()).setInDate(smallproLogDocument.getInDate())
                .setInUser(smallproLogDocument.getInUser()).setCommentType(smallproLogDocument.getCommentType())
                .setInformation(restulBuilder.toString());
        return smallproLogRes;
    }

    // endregion

    // region 渠道送修 repair

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SmallproNormalCodeMessageRes repair(Integer smallproId, String maintainChannel) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Smallpro smallpro = baseMapper.getByIdSqlServer(smallproId);
        if (smallpro == null) {
            result.setCode(500);
            result.setMessage("小件售后接件单错误！");
            return result;
        }
        if(!SmallProKindEnum.SMALL_PRO_KIND_SERVICE.getCode().equals(smallpro.getKind())){
            result.setCode(500);
            result.setMessage("非维修接件单不允许送修！");
            return result;
        }
        smallpro.setWxQudao(maintainChannel).setStats(SmallProStatsEnum.SMALL_PRO_STATS_SENDING_REPAIR.getCode());
        boolean flag = ((SmallproServiceImpl) AopContext.currentProxy()).updateById(smallpro);
        if (!flag) {
            result.setCode(500);
            result.setMessage("小件售后接件单数据库更新错误！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return result;
        }
        result.setCode(0);
        result.setMessage("送修成功！");
        return result;
    }

    // endregion

    // region 更新小件接件维修状态 updateSmallproRepairStatus

    @Override
    public SmallproNormalCodeMessageRes updateSmallproRepairStatus(Integer smallproId, String username,
                                                                   Integer maintainState) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Smallpro smallpro = baseMapper.getByIdSqlServer(smallproId);
        if (smallpro == null) {
            result.setCode(500);
            result.setMessage("请检查小件接件ID是否正确！无此ID小件接件单！");
            return result;
        }
        if (Boolean.TRUE.equals(smallpro.getIsDel())
                || SmallProStatsEnum.SMALL_PRO_STATS_DELETED.getCode().equals(smallpro.getStats())) {
            result.setCode(500);
            result.setMessage("小件单已删除不允许更新维修状态！");
            return result;
        }
        smallpro.setStats(0).setOwenStats(1).setWxState(maintainState).setWxUser(username).setWxTime(LocalDateTime.now());
        boolean updateFlag = ((SmallproServiceImpl) AopContext.currentProxy())
                .updateSmallproRepairStatusWrite(result, smallpro);
        if (!updateFlag) {
            return result;
        }
        if (smallpro.getKind() == 1 && maintainState == 1) {
            ShouhouMsgconfig shouhouMsgconfig =
                    shouhouMsgconfigService.getShouhouMsgconfigByLogType(ShouHouMsgConfigEnum.TYPE2.getCode());
            if (null != shouhouMsgconfig) {
                sendSmallProWeiXinMsg(smallpro.getUserId(), smallpro.getAreaId(), shouhouMsgconfig,
                        shouhouMsgconfig.getMsgcontent(), smallpro.getMobile(), smallpro.getId());
                smallproLogService.addLogs(smallpro.getId(), shouhouMsgconfig.getMsgcontent(), username, 1);
            }
        }
        result.setCode(0);
        result.setMessage("小件接件单维修状态更新成功！");
        return result;
    }


    // endregion

    // region 转地区操作 toArea

    @Override
    public SmallproNormalCodeMessageRes toArea(Integer smallproId, Integer areaId, Integer toAreaId, Integer type,
                                               OaUserBO oaUserBO, Boolean createWuliu, Smallpro smallpro) {
        if(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())
            && smallproBillService.existLockStock(smallproId, false)){
            throw new CustomizeException("换货商品已备货，请先取消备货再转地区操作");
        }
        //结果默认成功,如果异常会进行修改
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes().setCode(0).setMessage("转地区操作成功！");
        ;
        Areainfo senderInfo = areainfoService.getByIdSqlServer(areaId);
        Areainfo receiveInfo = areainfoService.getByIdSqlServer(toAreaId);
        Integer wType = LogisticsTypeEnum.LOGISTICS_TYPE_13.getCode();
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(areaId);
        if (receiveInfo.getId().equals(backEndInfo.getH1AreaId())
                || receiveInfo.getId().equals(backEndInfo.getDcAreaId())
                || receiveInfo.getId().equals(backEndInfo.getD1AreaId())
                || receiveInfo.getId().equals(JiujiAreaIdEnum.JIUJI_AREA_ID_H3.getCode())) {
            if (smallpro.getKind().equals(1)) {
                wType = LogisticsTypeEnum.LOGISTICS_TYPE_5.getCode();
            }
        }
        LogisticsRecipientBO receiver = receivePersonConfigService.getReceiverUserInfo(wType, senderInfo.getId(),
                receiveInfo.getId());
        boolean flag = ((SmallproServiceImpl) AopContext.currentProxy()).toAreaWrite(result, smallproId, areaId,
                toAreaId, type, oaUserBO, senderInfo, receiveInfo, receiver, createWuliu);
        if (flag && XtenantEnum.isSaasXtenant()) {
            //目前只做输出
            ChangeAreaInfo changeAreaInfo = new ChangeAreaInfo();
            changeAreaInfo.setSubId(smallproId);
            changeAreaInfo.setSubKinds(5);
            changeAreaInfo.setOldAreaId(areaId);
            changeAreaInfo.setNewAreaId(toAreaId);
            pushChangeAreaInfo(changeAreaInfo);
        }
        return result;
    }

    public void pushChangeAreaInfo(ChangeAreaInfo changeAreaInfo) {
        oaAsyncRabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, JSON.toJSONString(new SubPushMsgBO()
                .setAct("SubChangeArea").setData(changeAreaInfo)));
    }


    // endregion

    // region 根据小件单号列表批量转地区 toArea

    @Override
    public SmallproNormalCodeMessageRes toArea(List<Integer> smallproIdList, Integer toAreaId, Integer type,
                                               OaUserBO oaUserBO) {


        return null;
    }

    // endregion

    // region 小件接件预约确认 checkReserve

    @Override
    public SmallproNormalCodeMessageRes checkReserve(Integer smallproId, OaUserBO oaUserBO) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Smallpro smallpro = baseMapper.getByIdSqlServer(smallproId);
        if (smallpro == null || smallpro.getYuyueId() == null || smallpro.getYuyueId() <= 0) {
            result.setCode(500);
            result.setMessage("当前小件单号无接件单或当前小件单非预约生成的小件单！");
            return result;
        }
        UpdateWrapper<Smallpro> smallproUpdateWrapper = new UpdateWrapper<>();
        smallproUpdateWrapper.lambda().set(Smallpro::getInUser, oaUserBO.getUserName()).set(Smallpro::getYuyueCheck,
                1).eq(Smallpro::getId, smallproId);
        boolean flag = ((SmallproServiceImpl) AopContext.currentProxy()).update(smallproUpdateWrapper);
        if (!flag) {
            result.setCode(500);
            result.setMessage("数据库更新失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return result;
        }
        result.setCode(0);
        result.setMessage("预约确认成功！");
        return result;
    }


    /**
     * 小件connectionMethod字段处理
     * @param smallpro
     */
    private void handleConnectionMethod(Smallpro smallpro){
        try {
            String connectionMethod = smallpro.getConnectionMethod();
            if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(connectionMethod)){
                smallpro.setConnectionMethod("("+HttpClientUtil.getPlatformStr()+")"+connectionMethod);
            }
        } catch (Exception e){
            RRExceptionHandler.logError("小件单记录接件来源异常",Dict.create().set("connectionMethod",smallpro.getConnectionMethod()).set("id",smallpro.getId()), e, smsService::sendOaMsgTo9JiMan);
        }

    }
    private void saveConnectionMethod(SmallproReq smallpro){
        try {
            OaUserBO oaUserBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElse(new OaUserBO());
            String userName = Optional.ofNullable(oaUserBO.getUserName()).orElse("系统");
            String connectionMethod = smallpro.getConnectionMethod();
            // 接件方式日志记录
            StringBuilder log = new StringBuilder();
            if(StringUtils.isNotEmpty(connectionMethod)){
                String comment = "";
                if(HttpClientUtil.connectionMethodList().contains(connectionMethod)){
                    ShouhouYuyueService yuyueService = SpringUtil.getBean(ShouhouYuyueService.class);
                    comment = String.format("添加小件单，接件方式：%s，预约单号：%s", connectionMethod,yuyueService.getYuYueIdUrl(smallpro.getYuyueId()));
                } else {
                    comment = String.format("添加小件单，接件方式：%s", connectionMethod);
                }
                log.append(comment);
            }
            String connectionFid = smallpro.getConnectionFid();
            if(StringUtils.isNotEmpty(connectionFid)){
                String fidUrl = imageProperties.getSelectImgUrl() + "newstatic/" + connectionFid;
                log.append(": <a target= _blank class=\"blue\" href=\"" + fidUrl + "\">小图" + smallpro.getImei() + "</a>");
            }
            //串号大图获取
            MiniFileRes bigImeiFile = smallpro.getBigImeiFile();
            if(ObjectUtil.isNotNull(bigImeiFile)){
                String fidUrlBig = imageProperties.getSelectImgUrl() + "newstatic/" + bigImeiFile.getFid();
                log.append(" <a target= _blank class=\"blue\" href=\"" + fidUrlBig + "\">大图" + smallpro.getImei() + "</a>");
            }
            if(log.length()>NumberConstant.ZERO){
                smallproLogService.addLogs(smallpro.getId(), log.toString(), userName, 0);
            }
        } catch (Exception e){
            RRExceptionHandler.logError("小件单记录接件来源异常",Dict.create().set("connectionMethod",smallpro.getConnectionMethod()).set("connectionFid",smallpro.getConnectionFid()), e, smsService::sendOaMsgTo9JiMan);
        }

    }
    // endregion

    // region 添加接件信息 saveSmallpro

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R saveSmallpro(SmallproReq smallpro, Integer areaId, OaUserBO oaUserBO) {
        log.debug("保存小件接件单：" + JSONObject.toJSONString(smallpro));
        assertCheck(smallpro, areaId);
        // 设置费用
        setFeiYong(smallpro);
        // 暂时去掉30天无理由换新限制
/*        if ((smallpro.getServiceType() == null || smallpro.getServiceType() == 0)
                && smallpro.getIsFreeExchange() != null && !smallpro.getIsFreeExchange()) {
            smallpro.setChangePpriceid(null);
        }*/
        if (!smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode())
                || (null != smallpro.getChangePpriceid() && smallpro.getChangePpriceid() == 0)) {
            smallpro.setChangePpriceid(null);
        }
        //设置默认值
        if(SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode().equals(smallpro.getKind())){
            smallpro.setOldIdType(Optional.ofNullable(smallpro.getOldIdType()).orElse(OldIdTypeEnum.SALE_TYPE.getCode()));
        }
        //新增
        if (smallpro.getId() == null || smallpro.getId() == 0) {
//            LambdaQueryWrapper<Smallpro> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper
//                    .eq(Smallpro::getSubId, smallpro.getSubId())
//                    .eq(Smallpro::getStats, 0)
//                    .ne(Smallpro::getKind, 4)
//                    .and(bo -> bo.ne(Smallpro::getIsDel, 1)
//                            .or().isNull(Smallpro::getIsDel));
//            if (areaId != null && areaId != 0) {
//                queryWrapper.eq(Smallpro::getAreaId, areaId);
//            }

            //该订单存在进行中的小件接件订单，单号：XXX  ，请先处理进行中的小件单再处理预约单
            if (CommenUtil.isNotNullZero(smallpro.getSubId())) {
                List<Smallpro> list = baseMapper.getSmallProListBySubId(smallpro.getSubId(), null);
                if (CollectionUtils.isNotEmpty(list)) {
                    Integer id = list.get(0).getId();
                    return R.error("该订单存在进行中的小件接件订单，单号：" + id + " ，请先处理进行中的小件单再处理预约单");
                }
            }

            //判断我的壳膜(只有九机处理)
            if (XtenantEnum.isJiujiXtenant() && categoryService.determineShellAndFilm(smallpro)) {
                //判断识别码加单
                Optional.ofNullable(extendService.createMemberUserCodeRes(oaUserBO, smallpro.getUserId())).ifPresent(item -> {
                    Integer addSearchKind = item.getAddSearchKind();
                    if (NumberConstant.TWO.equals(addSearchKind)) {
                        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        smallpro.setCodeMsg(String.format("识别码验证 【%s】 %s", oaUserBO.getUserName(), time));
                    }
                });
            }
            smallpro.setName(StrUtil.maxLength(smallpro.getName(), NumberConstant.FIVE_HUNDRED - NumberConstant.THREE));
            //现货单增加userId保存
            if(ObjectUtil.defaultIfNull(smallpro.getOldId(), 0) > 0){
                if(OldIdTypeEnum.SMALL_PRO_TYPE.getCode().equals(smallpro.getOldIdType())){
                    Optional.ofNullable(this.getById(smallpro.getOldId())).ifPresent(item -> smallpro.setOldUserId(item.getUserId()));
                } else {
                    CommenUtil.autoQueryHist(() -> subService.lambdaQuery().eq(Sub::getSubId, smallpro.getOldId()).select(Sub::getUserId).list()
                                    , MTableInfoEnum.SUB, smallpro.getOldId()).stream()
                            .map(Sub::getUserId).filter(Objects::nonNull).findFirst().map(Convert::toInt)
                            .ifPresent(smallpro::setOldUserId);
                }
            }
            handleConnectionMethod(smallpro);
            ((SmallproServiceImpl) AopContext.currentProxy()).save(smallpro);
            //查询运营商抵扣金额
            BigDecimal offsetMoney = selectOffsetMoney(smallpro.getSmallproBillList());
            if(offsetMoney.compareTo(BigDecimal.ZERO)>0){
                String comment=String.format("添加维修费用：%s，来源：运营商业务办理违约金。", offsetMoney);
                smallproLogService.addLogs(smallpro.getId(), comment, "系统", 0);
            }

            saveConnectionMethod(smallpro);
            List<String> sysLogList = new LinkedList<>();
            //二次确认异常日志记录
            Optional.ofNullable(smallpro.getSecondaryConfirmationText()).ifPresent(sysLogList::add);
            boolean isLockKc = false;
            //插入退换记录
            if (SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())) {
                //换其他型号不需要生成退换记录
                boolean isHqtxh = isHqtxh(smallpro);
                if(!isHqtxh){
                    ShouhouTuihuan shouhouTuihuan = new ShouhouTuihuan();
                    shouhouTuihuan.setTuihuanKind(9);
                    shouhouTuihuan.setSubId(smallpro.getSubId());
                    shouhouTuihuan.setComment("小件换货");
                    shouhouTuihuan.setDtime(LocalDateTime.now());
                    shouhouTuihuan.setInuser(smallpro.getInUser());
                    shouhouTuihuan.setCheck1(true);
                    shouhouTuihuan.setAreaid(smallpro.getAreaId());
                    shouhouTuihuan.setSmallproid(smallpro.getId());
                    shouhouTuihuanService.save(shouhouTuihuan);
                    // 锁定库存
                    if(XtenantEnum.isJiujiXtenant() && smallpro.getChangePpriceid() != null){
                        isLockKc = true;
                    }
                }

                //记录匹配到的换货配置
                smallpro.getSmallproBillList().stream()
                        .filter(sb -> Objects.nonNull(smallpro.getChangePpriceid()) && ObjectUtil.defaultIfNull(smallpro.getExchangeConfigId(), 0) > 0)
                        .map(smallproBill -> {
                            Integer originChangePpid = ObjectUtil.defaultIfNull(smallpro.getLastChangeProductPpid(), Convert.toInt(smallproBill.getPpriceid()));
                            Productinfo productinfo = productinfoService.getProductinfoByPpid(originChangePpid);
                            Integer changePpriceid = smallpro.getChangePpriceid();
                            SmallExchangeConfigPo config = smallProConfigService.getConfigById(smallpro.getExchangeConfigId());
                            // 换其他型号无需记录差价信息, 在新机单上单独收银
                            String message = isHqtxh ? "" : getFilmAccessoriesV2Items(smallproBill.getBasketId(), Convert.toStr(changePpriceid), config,
                                    productinfo, smallproBill.getMobileExchangeFlag(), "").stream().findFirst()
                                    .map(FilmAccessoriesV2Item::getMassage).orElse("");
                            return StrUtil.format("原商品ppid: {}, 使用的换货配置编号: {},配置名称: {} {}", originChangePpid,
                                    config.getId(), config.getTitle(), message);
                        })
                        .forEach(sysLogList::add);

            }
            //添加退换修商品
            if (CollectionUtils.isNotEmpty(smallpro.getSmallproBillList())) {
                List<SmallproBill> proBillList =
                        smallpro.getSmallproBillList().stream().map(e -> e.setSmallproID(smallpro.getId())).collect(Collectors.toList());
                for (SmallproBill temp : proBillList) {
                    //现货不存旧件成本
                    BigDecimal lastExchangePpidInprice = null;
                    if (ObjectUtil.defaultIfNull(temp.getBasketId(), 0) > 0) {
                        lastExchangePpidInprice = Optional.ofNullable(baseMapper.getLastExchangePpidInpriceByBasketId(temp.getBasketId())).orElse(BigDecimal.ZERO);
                        if (lastExchangePpidInprice.compareTo(BigDecimal.ZERO) <= NumberConstant.ZERO.intValue()) {
                            if (Convert.toBool(temp.getMobileExchangeFlag())) {
                                lastExchangePpidInprice = BigDecimal.ZERO;
                                List<ProductKc> productKcList = listProductKc(oaUserBO, Arrays.asList(Convert.toInt(temp.getPpriceid())));
                                if (CollectionUtils.isNotEmpty(productKcList)) {
                                    lastExchangePpidInprice = productKcList.get(0).getInprice();
                                }
                            } else {
                                //获取订单价格
                                lastExchangePpidInprice = Optional.ofNullable(CommenUtil
                                                .autoQueryHist(()-> basketService.getById(temp.getBasketId()), MTableInfoEnum.BASKET, temp.getBasketId()))
                                        .map(Basket::getInprice).orElse(BigDecimal.ZERO);
                            }
                        }
                    }
                    temp.setInprice(lastExchangePpidInprice);
                    smallproBillService.save(temp);
                }
                // 锁定库存
                if(isLockKc){
                    saveSmallproLockKc(smallpro, oaUserBO);
                }
            }
            // 保存日志，发送微信消息
            ShouhouMsgconfig shouhouMsgconfig =
                    shouhouMsgconfigService.getShouhouMsgconfigByLogType(ShouHouMsgConfigEnum.TYPE1.getCode());
            String printName = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.PRINT_NAME, oaUserBO.getXTenant());
            if (null != shouhouMsgconfig) {
                String format = MessageFormat.format(shouhouMsgconfig.getMsgcontent(), printName);
                sendSmallProWeiXinMsg(smallpro.getUserId(), areaId, shouhouMsgconfig, format, smallpro.getMobile(),
                        smallpro.getId());
                smallproLogService.addLogs(smallpro.getId(), format, oaUserBO.getUserName(), 1);
            }
            sysLogList.forEach(log -> smallproLogService.addLogs(smallpro.getId(), log, oaUserBO.getUserName(), 0));
//            smsService.sendDiamondMemberMsgWhenBusiness(smallpro.getUserId(),smallpro.getAreaId(), BusinessTypeEnum.AFTER_SMALL_ORDER.getCode(),smallpro.getId());
            CompletableFuture.runAsync(() -> smsService.sendDiamondMemberMsgWhenBusiness(smallpro.getUserId(), smallpro.getAreaId(), BusinessTypeEnum.AFTER_SMALL_ORDER.getCode(), smallpro.getId()));

            //接件成功，记录客户与会员绑定关系
            CompletableFuture.runAsync(() -> smsService.pushMemberScanBind(Math.toIntExact(smallpro.getUserId()), oaUserBO.getUserId(), BusinessTypeEnum.AFTER_SMALL_ORDER.getCode(), smallpro.getId(), BusinessNodeEnum.BUSINESS_BEGIN.getCode()));

        } else {
            Integer kind = 0;
            Smallpro smallpro1 = baseMapper.selectById(smallpro.getId());
            if (smallpro1 != null) {
                kind = smallpro1.getKind();
            }
            if (!kind.equals(smallpro.getKind())) {
                //添加日志
                StringBuffer sb = new StringBuffer("处理方式由");
                sb.append(EnumUtil.getMessageByCode(SmallProKindEnum.class, kind));
                sb.append("改为");
                sb.append(EnumUtil.getMessageByCode(SmallProKindEnum.class, smallpro.getKind()));
            }
            ((SmallproServiceImpl) AopContext.currentProxy()).updateById(smallpro);
        }
        //处理kindType
        addSmallproKindtype(smallpro.getId(), smallpro.getSmallProKinds());
        saveSmallproAttachments(smallpro.getFilesList(), oaUserBO, smallpro.getId());
        //扫码串号大图保存
        saveBigImeiAttachments(smallpro.getBigImeiFile(), oaUserBO, smallpro.getId());
        //执行日志lambda
        SpringContextUtil.runAndRemoveRequestLambda();
        CompletableFuture.runAsync(() -> {
            //发送加单通知 xxk 2021-09-24
            String pushMsg = JSON.toJSONString(new SubPushMsgBO()
                    .setAct("subAddMsgPushToFollow").setData(new SubPushMsgBO.DataBO().setSubType(BusinessTypeEnum.AFTER_SMALL_ORDER)
                            .setOpType(SubPushMsgBO.OpTypeEnum.ADD_SUB).setSubId(smallpro.getId()).setUserId(Long.valueOf(DecideUtil.isNull(smallpro.getUserId(), 0)))));
            log.info("推送小件加单消息:{}", pushMsg);
            rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, pushMsg);
            //判断运营商是否需要自动收银（不需要管收银是否成功）
            Boolean isAutoOperatorShouYin = Optional.ofNullable(smallpro.getIsAutoOperatorShouYin()).orElse(Boolean.FALSE);
            if(isAutoOperatorShouYin){
                AutoOperatorShouYin(smallpro,oaUserBO);
            }
        });
        return R.success(smallpro);
    }

    /**
     * 自动收银
     * @param smallpro
     */
    private void AutoOperatorShouYin(SmallproReq smallpro,OaUserBO oaUserBO){
        List<SmallproBill> smallproBillList = smallpro.getSmallproBillList();
        if(CollUtil.isEmpty(smallproBillList)){
            return;
        }
        Long ppriceid = smallproBillList.get(NumberConstant.ZERO).getPpriceid();
        Integer xTenant = oaUserBO.getXTenant();
        //调用运营商order服务获取三方支付id
        TripartitePaymentIdReq paymentIdReq = new TripartitePaymentIdReq();
        paymentIdReq.setPpid(Convert.toInt(ppriceid));
        paymentIdReq.setXtenant(xTenant);
        R<Integer> res = SpringUtil.getBean(OrderDetailCloud.class).selectTripartitePaymentId(paymentIdReq);
        if(!res.isSuccess() || ObjectUtil.isNull(res.getData())){
            return;
        }
        Integer otherPayType = res.getData();
        //调用C#接口进行自动收银
        AutoOperatorShouReq req = new AutoOperatorShouReq();
        req.setSmallproid(smallpro.getId());
        req.setPayprice(smallpro.getFeiyong());
        req.setPayuser(oaUserBO.getUserName());
        req.setOtherPayType(otherPayType);
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData)
                .filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/oa/smallpro/otherpay";
        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                .header("authorization", oaUserBO.getToken())
                .body(JSONUtil.toJsonStr(req))
                .execute();
        log.warn("运营商自动收银传入参数：{}，返回结果：{}，传入userBO:{}",req,evidenceResult.body(),JSONUtil.toJsonStr(oaUserBO));
        if(!evidenceResult.isOk()){
            log.warn("运营商自动收银调用异常传入参数：{}",evidenceUrl);
        }
    }

    /**
     *
     * @param smallpro
     * @param oaUserBO
     */
    private void saveSmallproLockKc(SmallproReq smallpro, OaUserBO oaUserBO) {
        Productinfo productinfo = productinfoService.getProductinfoByPpid(smallpro.getChangePpriceid());
        //获取当前门店的可用库存
        Integer changePpriceid1 = productinfo.getPpriceid1();
        Integer kcCount = ObjectUtil.defaultIfNull(productKcService.getKcCount(changePpriceid1, smallpro.getAreaId()), 0);
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "换货ppid: {}, ppid1: {},areaId: {} 当前门店库存: {}",
                smallpro.getChangePpriceid(), changePpriceid1, smallpro.getAreaId(), kcCount);
        // 构建同城备货对象
        List<SmallProBeihuoAutoReq.SmallProDetail> autoSmallProDetails = new LinkedList<>();
        for (SmallproBill smallproBill : smallpro.getSmallproBillList()) {
            // 锁定量
            int lockCount = NumberUtil.min(smallproBill.getCount(), kcCount);
            // 同城备货量
            int autoBeiHuoCount = smallproBill.getCount() - lockCount;
            if(autoBeiHuoCount >0){
                autoSmallProDetails.add(SmallProBeihuoAutoReq.SmallProDetail.builder().billId(smallproBill.getId())
                        .count(autoBeiHuoCount).ppriceid(changePpriceid1).build());
            }
            if(lockCount <=0){
                continue;
            }
            //调用接口锁定库存
            Boolean lockR = productKcService.lockKc(changePpriceid1, smallpro.getAreaId(), lockCount);
            if(!Boolean.TRUE.equals(lockR)){
                throw new CustomizeException("库存锁定异常, 请稍后重试");
            }
            //生成锁定记录
            SpringUtil.getBean(ProductKcLockInfoService.class).save(ProductKcLockInfo.builder()
                    .lockCount(lockCount).basketId(smallproBill.getId())
                    .basketType(StockBasketTypeEnum.SMALL_PRODUCT_ORDER.getCode())
                    .isDone(false).areaId(smallpro.getAreaId()).ppriceId(changePpriceid1)
                    .createTime(LocalDateTime.now()).createUser(oaUserBO.getUserName())
                    .build());
            SpringContextUtil.addRequestLambda(() -> addStockLog(SmallproBillLogVo.builder().billId(smallproBill.getId())
                    .content(StrUtil.format("备货锁定，数量：{}", lockCount)).type(1).build()));
            //剩余库存进行扣减
            kcCount -= lockCount;
        }
        if(!autoSmallProDetails.isEmpty()){
            //同城备货
            SmallProBeihuoAutoReq beihuoAutoReq = SmallProBeihuoAutoReq.builder().smallproId(smallpro.getId())
                    .areaId(smallpro.getAreaId()).smallPros(autoSmallProDetails).build();
            smallpro.setBeihuoAutoReq(beihuoAutoReq);
        }
    }

    private void assertCheck(SmallproReq smallpro, Integer areaId) {
        //判断是否同体系门店接件
//        if(XtenantEnum.isJiujiXtenant()){
//            Optional<AreaInfo> areaInfoOpt = Optional.ofNullable(areaInfoClient.getAreaInfoById(areaId)).filter(R::isSuccess).map(R::getData);
//            Optional.ofNullable(smallpro.getSubId()).map(subId -> SpringUtil.getBean(SubService.class)
//                    .getOne(new LambdaQueryWrapper<Sub>().select(Sub::getAreaId).eq(Sub::getSubId, subId)))
//                    .map(sub -> sub.getAreaId()).map(areaInfoClient::getAreaInfoById).filter(R::isSuccess)
//                    .map(R::getData)
//                    //不是同一体系门店
//                    .filter(subAreaInfo -> !BusinessUtil.jiujiSameAuthArea(subAreaInfo,areaInfoOpt.orElse(null)))
//                    //退换货条件
//                    .filter(buyAreaInfo -> Arrays.asList(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode(),SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode()).contains(smallpro.getKind()))
//                    //非年包服务出险
//                    .filter(buyAreaInfo -> !Arrays.asList(SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode()).contains(smallpro.getServiceType()))
//                    .ifPresent(buyAreaInfo ->{
//                        throw new CustomizeException(StrUtil.format("设备购买地为{}，请在该门店后台体系下进行小件接件办理",buyAreaInfo.getArea()));
//                    });
//        }
        if (Objects.equals(smallpro.getKind(), SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode())
                && ObjectUtil.defaultIfNull(smallpro.getUserId(), 0) > 0) {
            //小件退货黑名单验证
            CsharpCommonService csharpCommonService = SpringUtil.getBean(CsharpCommonService.class);
            R<List<BlackListVo>> blackListsR = Optional.ofNullable(Convert.toInt(smallpro.getUserId())).map(csharpCommonService::getBlackLists)
                    .orElseGet(() -> R.success(null));
            if (!blackListsR.isSuccess()) {
                throw new CustomizeException(blackListsR.getUserMsg());
            }
            List<BlackListVo> blackSubTypes = ObjectUtil.defaultIfNull(blackListsR.getData(), (List<BlackListVo>) Collections.EMPTY_LIST)
                    .stream().filter(Objects::nonNull).collect(Collectors.toList());
            List<Integer> blackSubTypeCodes = blackSubTypes.stream().map(BlackListVo::getCode).collect(Collectors.toList());
            boolean isYoupin = basketService.lambdaQuery().select(Basket::getBasketId)
                    .in(Basket::getBasketId, smallpro.getSmallproBillList().stream().map(SmallproBill::getBasketId).collect(Collectors.toList()))
                    .apply("isnull(isdel,0)=0").eq(Basket::getType, 22).count() > 0;
            if (isYoupin && blackSubTypeCodes.contains(MemberBlacklist.YOUPIN.getCode())) {
                throw new CustomizeException(StrUtil.format("系统检测到您近期多次退款优品订单，退货频率异常，系统已限制无理由退款{}，但不影响您正常购买商品。若有疑问，请咨询客服。",
                        ShouhouExServiceImpl.getLockDurationText(blackSubTypes, MemberBlacklist.YOUPIN)));
            }
            if (!isYoupin && blackSubTypeCodes.contains(MemberBlacklist.NEWSUBBACK.getCode())) {
                throw new CustomizeException(StrUtil.format("系统检测到您近期多次退款新机订单，退货频率异常，系统已限制无理由退款{}，但不影响您正常购买商品。若有疑问，请咨询客服。",
                        ShouhouExServiceImpl.getLockDurationText(blackSubTypes, MemberBlacklist.NEWSUBBACK)));
            }
        }
        List<Integer> billBasketIds = smallpro.getSmallproBillList()
                .stream().map(SmallproBill::getBasketId).collect(Collectors.toList());

        if (Objects.equals(smallpro.getKind(), SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode())
                && CollUtil.count(smallpro.getSmallproBillList(), null) > 0) {
            assertCheckFilmCard(billBasketIds);
        }

        // 存在大件不允许提交退款
        List<Integer> mobilePpids = basketService.lambdaQuery().select(Basket::getPpriceid)
                .in(Basket::getBasketId, billBasketIds).eq(Basket::getIsmobile, Boolean.TRUE).list().stream()
                .map(Basket::getPpriceid).distinct()
                .filter(ppid -> smallpro.getSmallproBillList().stream().map(SmallproBill::getPpriceid)
                        .anyMatch(sbPpid -> Objects.equals(sbPpid, ppid)))
                .map(Convert::toInt)
                .collect(Collectors.toList());
        if (mobilePpids.size() > 0) {
            throw new CustomizeException(StrUtil.format("大件商品[{}]不能进行小件接件", mobilePpids.stream().map(Convert::toStr)
                    .collect(Collectors.joining(StringPool.COMMA))));
        }
        // 【九讯云】 分销政企用户售后流程限制加单功能开发
        CustomerAccountService customerAccountService = SpringUtil.getBean(CustomerAccountService.class);
        if (XtenantEnum.isSaasXtenant()
                && ObjectUtil.notEqual(CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant()), ShortXtenantEnum.ZLF.getCode())
                && !Objects.equals(smallpro.getKind(), SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode())
                // 接件人为分销用户
                && (ObjectUtil.defaultIfNull(smallpro.getUserId(), 0) > 0
                && ObjectUtil.defaultIfNull(SpringUtil.getBean(CustomerAccountService.class).getCustomerKinds(smallpro.getUserId()), 0) > 0
                // 原订单为分销订单
                || ObjectUtil.defaultIfNull(customerAccountService.getCustomerKinds(SpringUtil.getBean(SubService.class).getUserIdBySubId(smallpro.getSubId())), 0) > 0)
        ) {
            throw new CustomizeException("分销、政企用户只能进行[退款]方式小件接件");
        }

        //没有预约单情况下才校验, 小件换货, 如果没有换货配置id, 不允许提交
        if (ObjectUtil.defaultIfNull(smallpro.getYuyueId(), 0) <= 0
                && Objects.equals(smallpro.getKind(), SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode())) {
            SmallproBill smallproBill = smallpro.getSmallproBillList().get(0);
            //换不同的ppid才需要校验
            if(ObjectUtil.notEqual(Convert.toInt(smallproBill.getPpriceid()), smallpro.getChangePpriceid())){
                if(ObjectUtil.defaultIfNull(smallpro.getExchangeConfigId(), 0) <=0){
                    throw new CustomizeException("换货配置不能为空");
                }
                R<List<FilmAccessoriesV2Item>> filmResult = getFilmAccessoriesV3(Convert.toInt(smallproBill.getPpriceid()), smallpro.getServiceType(), smallproBill.getBasketId(),
                        Convert.toStr(smallpro.getChangePpriceid()), smallproBill.getMobileExchangeFlag(), smallpro.getImei());
                if(!filmResult.isSuccess()){
                    throw new CustomizeException(filmResult);
                }
                if (CollUtil.isEmpty(filmResult.getData())){
                    throw new CustomizeException(StrUtil.format("配置不允许换商品ppid[{}]", smallpro.getChangePpriceid()));
                }
            }
        }

        //接件商品数量不能为0
        List<Long> billZeroPpids = smallpro.getSmallproBillList()
                .stream().filter(sb -> ObjectUtil.defaultIfNull(sb.getCount(), 0) <= 0)
                .map(SmallproBill::getPpriceid).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(billZeroPpids)){
            throw new CustomizeException(StrUtil.format("接件商品ppid[{}]数量不允许小于等于0", billZeroPpids.stream()
                    .map(Convert::toStr).collect(Collectors.joining(StringPool.COMMA))));
        }

        // 国补订单校验
        if(XtenantEnum.isJiujiXtenant()
                && Objects.equals(smallpro.getKind(), SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode())
                // 换货商品不全为附件才进行校验
                && smallpro.getSmallproBillList().size() != smallpro.getSmallproBillList().stream()
                .filter(sbill -> Convert.toBool(sbill.getMobileExchangeFlag(), Boolean.FALSE)).count()
        ){
            SpringContextUtil.getRequest()
                    // 超过30天购买的就不需要校验了
                .filter(req -> smallpro.getBuyDate() != null && smallpro.getBuyDate().isBefore(LocalDateTime.now().minusDays(30)))
                    .ifPresent(req -> req.setAttribute(RequestAttrKeys.DOUYIN_SERVICE_CAN_REFUND_PRICE_NOT_EX, true));
            ZheJiaPayEnum zheJiaPayEnum = SpringUtil.getBean(ZheJiaPayService.class).getZheJiaPayEnum(smallpro.getSubId(), TuihuanKindEnum.TPJ);
            if((zheJiaPayEnum != null && ZheJiaPayEnum.GUO_JIA_BUTIE.equals(zheJiaPayEnum))){
                throw new CustomizeException("国补订单不能进行换货操作，若要换货需退款后重新交易");
            }
        }
    }

    @Override
    public void assertCheckFilmCard(List<Integer> billBasketIds) {
        if(CollUtil.isEmpty(billBasketIds)){
            //参数为空不需要校验
            return;
        }
        //小件退货年包验证
        List<FilmCardInfomationBO> filmCardInfomations = smallproFilmCardService.getFilmCardInfoByBasketIdByWrite(billBasketIds);
        if(filmCardInfomations.stream().anyMatch(fci -> billBasketIds.stream().noneMatch(bbi -> Objects.equals(bbi,fci.getBasketId())))){
            throw new CustomizeException(DecideUtil.iif(filmCardInfomations.stream()
                            .anyMatch(fci -> ObjectUtil.defaultIfNull(fci.getUseCount(),0)>0 && !Boolean.TRUE.equals(fci.getIsCanRefund())),
                    ()->"年包已使用,不允许退",()->"请先退年包(年包未使用才允许退)"));
        }
        //服务退货验证
        Optional<String> useMsgOpt = checkServiceRefund(billBasketIds);
        if(useMsgOpt.isPresent()){
            throw new CustomizeException(useMsgOpt.get());
        }
    }

    private Optional<String> checkServiceRefund(List<Integer> billBasketIds) {
        //服务退货验证
        ServiceRecordService serviceRecordService = SpringUtil.getBean(ServiceRecordService.class);
        List<ServiceRecord> serviceRecords = CommonUtils.bigDataInQuery(billBasketIds, ids ->
                serviceRecordService.lambdaQuery().in(ServiceRecord::getBasketId, ids).list());
        Map<Integer, List<ServiceRecord>> childServicesMap = serviceRecords.stream().filter(sr -> ObjectUtil.defaultIfNull(sr.getServicesTypeBindId(), 0) > 0)
                .collect(Collectors.groupingBy(ServiceRecord::getServicesTypeBindId));
        //验证是否已退款
        Optional<ServiceRecord> delSrOpt = serviceRecords.stream().filter(sr -> Boolean.TRUE.equals(sr.getIsdel())).findFirst();
        if (delSrOpt.isPresent()) {
            throw new CustomizeException(StrUtil.format("{}服务已退货,不允许重复退",
                    delSrOpt.map(sr -> ObjectUtil.defaultIfBlank(EnumUtil.getMessageByCode(ServiceEnum.class, sr.getServiceType()),
                            Convert.toStr(sr.getServiceType()))).orElse(null)));
        }
        //验证是否已经出险
        Optional<String> useMsgOpt = serviceRecords.stream()
                //处理主服务
                .filter(sr -> ObjectUtil.defaultIfNull(sr.getServicesTypeBindId(), 0) <= 0)
                .flatMap(sr -> {
                    List<ServiceRecord> childServices = childServicesMap.getOrDefault(sr.getId(), Collections.emptyList());
                    AtomicReference<com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum> stopSubTypeRef = new AtomicReference<>();
                    AtomicReference<Integer> stopOrderIdRef = new AtomicReference<>();
                    ServiceEnum parentServiceEnum = EnumUtil.getEnumByCode(ServiceEnum.class, sr.getServiceType());
                    if (childServices.isEmpty()) {
                        serviceRecordService.getStopServiceId(sr, parentServiceEnum, sr.getStartTime(), sr.getEndTime(), (stopSubType, stopOrderId) -> {
                            stopSubTypeRef.set(stopSubType);
                            stopOrderIdRef.set(stopOrderId);
                        });
                    } else {
                        return childServices.stream().map(cs -> {
                            ServiceEnum childServiceEnum = EnumUtil.getEnumByCode(ServiceEnum.class, cs.getServiceType());
                            serviceRecordService.getStopServiceId(cs, childServiceEnum, cs.getStartTime(), cs.getEndTime(), (stopSubType, stopOrderId) -> {
                                stopSubTypeRef.set(stopSubType);
                                stopOrderIdRef.set(stopOrderId);
                            });
                            if (stopOrderIdRef.get() != null) {
                                return StrUtil.format("{}服务商品不能进行退款操作，原因：{}赠送的{}服务已出险，{}单号: {}",
                                        parentServiceEnum.getMessage(), parentServiceEnum.getMessage(), childServiceEnum.getMessage(),
                                        stopSubTypeRef.get().getMessage(), stopOrderIdRef.get());
                            }
                            return null;
                        });
                    }
                    if (stopOrderIdRef.get() != null) {
                        return Stream.of(StrUtil.format("{}服务商品不能进行退款操作，原因：{}服务已出险，{}单号: {}",
                                parentServiceEnum.getMessage(), parentServiceEnum.getMessage(), stopSubTypeRef.get().getMessage(), stopOrderIdRef.get()));
                    }
                    return Stream.empty();
                }).filter(StrUtil::isNotBlank).findFirst();
        return useMsgOpt;
    }

    @Override
    public void sendSmallProWeiXinMsg(Integer userId, Integer areaId, ShouhouMsgconfig shouhouMsgconfig,
                                      String format, String mobile, Integer smallproId) {
        String wxMsg = format;
        String url = "/after-service/small-detail/" + smallproId;
        String handleType = "";
        if(XtenantEnum.isJiujiXtenant()){
            handleType  = Optional.ofNullable(getHandleTypeValue(smallproId)).orElse(shouhouMsgconfig.getFuwutxt());
        } else {
            handleType = shouhouMsgconfig.getFuwutxt();
        }
        log.warn("小件单微信推送小件单号：{}，handleType：{}", smallproId, handleType);
        String status = shouhouMsgconfig.getChulitxt();
        String logs = shouhouMsgconfig.getJindutxt();
        Integer pushType = shouhouMsgconfig.getPushtype();
        sendWeixinMsg(wxMsg, url, handleType, status, areaId, userId, logs, pushType,
                mobile);
    }

    /**
     * 获取推送内容
     * @param smallproId
     * @return
     */
    private String getHandleTypeValue(Integer smallproId){
        try {
            Smallpro smallpro = this.getById(smallproId);
            if(ObjectUtil.isNull(smallpro)){
                return "";
            }
            Integer kind = smallpro.getKind();
            Integer serviceType = smallpro.getServiceType();
            List<SmallproBill> smallproBillList = smallproBillService.list(new LambdaQueryWrapper<SmallproBill>().eq(SmallproBill::getSmallproID, smallpro.getId()));
            if(CollectionUtils.isEmpty(smallproBillList)){
                return "";
            }
            SmallproBill smallproBill = smallproBillList.get(NumberConstant.ZERO);
            Integer ppriceid = Optional.ofNullable(smallproBill.getPpriceid()).orElse(0L).intValue();
            //维修类型处理
            if(SmallProKindEnum.SMALL_PRO_KIND_SERVICE.getCode().equals(kind)){
                return "配件维修";
            } else if(SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode().equals(kind)){
                //退货 处理
                //判断是否九机服务商品退款
                if(smallproBillList.size()>NumberConstant.ONE){
                    return "配件退款";
                } else {
                    Integer basketId = smallproBill.getBasketId();
                    List<ServiceProductBO> serviceProductBOS = CommenUtil.autoQueryHist(()->this.baseMapper.selectServiceProductByBasketId(basketId));
                    if(CollectionUtils.isEmpty(serviceProductBOS)){
                        return "配件退款";
                    } else {
                        return "九机服务退款";
                    }
                }
            } else if(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(kind)){
                //换货处理
                if(smallproBillList.size()>NumberConstant.ONE){
                    return "配件换新";
                } else {
                    //保护膜分类商品换货
                    Boolean determineFilmByPpid = categoryService.determineFilmByPpid(ppriceid);
                    //保护壳分类商品换货
                    Boolean determineShellByPpid = categoryService.determineShellByPpid(ppriceid);
                    if(determineFilmByPpid && SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode().equals(serviceType)){
                        return "贴膜年包服务";
                    }else if(determineFilmByPpid){
                        return "贴膜质保换新";
                    } else if(determineShellByPpid && SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode().equals(serviceType)){
                        return "保护壳年包服务";
                    } else if (determineShellByPpid){
                        return "保护壳质保换新";
                    } else {
                        return "配件换新";
                    }
                }
            }
        } catch (Exception e){
            RRExceptionHandler.logError("小件单微信推送异常", smallproId, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        return "";
    }

    /**
     * 查询运营商抵扣金额
     * @param smallproBillList
     * @return
     */
    private BigDecimal selectOffsetMoney(List<SmallproBill> smallproBillList){
        if(XtenantEnum.isSaasXtenant() || CollUtil.isEmpty(smallproBillList)){
            return BigDecimal.ZERO;
        }
        SmallproBill smallproBill = Optional.ofNullable(smallproBillList.get(NumberConstant.ZERO)).orElse(new SmallproBill());
        Integer basketId = smallproBill.getBasketId();
        Basket basket = Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.getById(basketId), MTableInfoEnum.BASKET, basketId)).orElse(new Basket());
        Long subId = basket.getSubId();
        if(!SpringUtil.getBean(SmallproDetailsExService.class).isRecentMonthSub(subId)){
            List<Integer> basketIds = smallproBillList.stream().map(SmallproBill::getBasketId).collect(Collectors.toList());
            BigDecimal offsetMoney = this.baseMapper.selectOperatorBasketByBasketId(basketIds).stream()
                    .filter(item -> NumberConstant.ONE.equals(item.getStatus()))
                    .map(OperatorBasketReq::getOffsetMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(offsetMoney.compareTo(BigDecimal.ZERO)>0){
                return offsetMoney.setScale(2, RoundingMode.HALF_UP);
            }
        }
        return BigDecimal.ZERO;
    }

    private void setFeiYong(SmallproReq smallpro) {

        //费用
        BigDecimal cost = BigDecimal.valueOf(0.0);
        //获取原始费用
        BigDecimal originalFeiyong = Optional.ofNullable(smallpro).orElse(new SmallproReq()).getFeiyong();
        //判断如果过是换其他型号那维修费用就是0
        if(isHqtxh(smallpro)){
            smallpro.setFeiyong(cost);
            try {
                String comment=String.format("新版换其他型号费用由：%s修改为%s", originalFeiyong,cost);
                log.warn("新版换其他型号费用设置为 0:传入参数：{}",JSONUtil.toJsonStr(smallpro));
                OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElse(new OaUserBO());
                smallproLogService.addLogs(smallpro.getId(), comment, Optional.ofNullable(userBO.getUserName()).orElse("系统"), 0);
            }catch (Exception e){
                RRExceptionHandler.logError("新版换其他型号费用设置为0日志记录异常", smallpro, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
            return;
        }
        //意外换新服务 费用为售价的20%
        if (smallpro.getServiceType() != null && smallpro.getServiceType() == 1) {
            Integer basketId = 0;
            if (smallpro.getId() == null || smallpro.getId() == 0) {
                if (CollectionUtils.isNotEmpty(smallpro.getSmallproBillList())) {
                    basketId = smallpro.getSmallproBillList().get(0).getBasketId();
                }
            } else {
                List<SmallproBill> smallproBillList =
                        smallproBillService.list(new LambdaQueryWrapper<SmallproBill>().eq(SmallproBill::getSmallproID, smallpro.getId()));
                if (CollectionUtils.isNotEmpty(smallproBillList)) {
                    basketId = smallproBillList.get(0).getBasketId();
                }
            }
            final Integer finalBasketId = basketId;
            Basket basket = Optional.ofNullable(basketService.getByIdSqlServer(basketId)).orElseGet(() -> {
                AtomicReference<Basket> basketRef = new AtomicReference<>();
                //主库查询不到查询历史库
                new MultipleTransaction().execute(DataSourceConstants.OA_NEW_HIS, () -> basketRef.set(basketService.getByIdSqlServer(finalBasketId)))
                        .commit();
                return basketRef.get();
            });
            if (basket.getPrice() != null) {
                cost = basket.getPrice().multiply(BigDecimal.valueOf(0.2));
                smallpro.setFeiyong(cost);
            }
        }
        List<Integer> mobileExchangeFlagList = smallpro.getSmallproBillList().stream().map(SmallproBill::getMobileExchangeFlag).collect(Collectors.toList());
        if (mobileExchangeFlagList.contains(NumberConstant.ONE)) {
            Long ppriceid = smallpro.getSmallproBillList().get(0).getPpriceid();
            Productinfo pinfo = productinfoService.getProductinfoByPpid(Convert.toInt(ppriceid));
            BigDecimal memberPrice = ObjectUtil.defaultIfNull(pinfo.getMemberprice(), BigDecimal.ZERO);
            BigDecimal mobileAccessoriesExchangePrice = new BigDecimal(ApolloKeys.getApolloProperty(ApolloKeys.MOBILE_ACCESSORIES_EXCHANGE_PRICE, "200"));
            BigDecimal subtract = new BigDecimal(0);
            if (memberPrice.compareTo(mobileAccessoriesExchangePrice) > 0) {
                subtract = memberPrice.subtract(mobileAccessoriesExchangePrice);
            }
            smallpro.setFeiyong(subtract);
        }
        //查询运营商抵扣金额
        BigDecimal offsetMoney = selectOffsetMoney(smallpro.getSmallproBillList());
        if(offsetMoney.compareTo(BigDecimal.ZERO)>0){
            smallpro.setFeiyong(Optional.ofNullable(smallpro.getFeiyong()).orElse(BigDecimal.ZERO).add(offsetMoney));
            smallpro.setIsAutoOperatorShouYin(Boolean.TRUE);
        }
    }

    // endregion

    // region 根据返厂旧件单号获取小件接件单号 getSmallproIdByReturnFactoryId

    @Override
    public Integer getSmallproIdByReturnFactoryId(Integer returnFactoryId) {
        Boolean flag = true;
        List<SmallproKcRelatedInfoBO> list = baseMapper.getKcRelatedIdType(returnFactoryId);
        if (list == null || list.size() <= 0) {
            return null;
        }
        for (SmallproKcRelatedInfoBO bo : list) {
            if (bo.getComment().contains("售前")) {
                flag = false;
            }
        }
        if (flag) {
            ShouhouFanchang shouhouFanchang = shouhouFanchangService.getByIdSqlServer(returnFactoryId);
            if (shouhouFanchang != null && shouhouFanchang.getSmallproid() != null && shouhouFanchang.getSmallproid() > 0) {
                return shouhouFanchang.getSmallproid();
            }
        } else {
            SmallproBill smallproBill = smallproBillService.getByIdSqlServer(returnFactoryId);
            if (smallproBill != null && smallproBill.getSmallproID() != null && smallproBill.getSmallproID() > 0) {
                return smallproBill.getSmallproID();
            }
        }
        return null;
    }

    // endregion

    // region 一键备货 oneClickStocking

    @Override
    public SmallproNormalCodeMessageRes oneClickStocking(Integer smallproId, OaUserBO oaUserBO) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Smallpro smallpro = baseMapper.getByIdSqlServerByWriter(smallproId);
        // 已删除的小件单,不可以一键备货
        if (smallpro == null || Boolean.TRUE.equals(smallpro.getIsDel())
                || SmallProStatsEnum.SMALL_PRO_STATS_DELETED.getCode().equals(smallpro.getStats())) {
            result.setCode(500);
            result.setMessage("小件单不存在或已删除！");
            return result;
        }
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallproId);
        List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        if (smallpro.getHhSubId() != null && smallpro.getHhSubId() > 0) {
            result.setCode(500);
            result.setMessage("已生成过备货订单: " + smallpro.getHhSubId());
            return result;
        } else {
            if (smallpro.getKind() == 2 && smallpro.getStats() == 0) {
                SmallproOneClickStockUserInfoBO userInfoBO =
                        baseMapper.getCh999UserInfoByOneClickStocking(oaUserBO.getUserId());
                if (userInfoBO == null || userInfoBO.getBbsxpId() == null || userInfoBO.getBbsxpId() <= 0) {
                    result.setCode(500);
                    result.setMessage("不存在该员工或该员工尚未绑定九机会员，请用您OA通讯录的手机号码先注册一个九机会员！");
                    return result;
                }
                SmallproOneClickStockingSubBO subBO = new SmallproOneClickStockingSubBO();
                subBO.setUserid(userInfoBO.getBbsxpId()).setSub_to(oaUserBO.getUserName())
                        .setSub_tel(userInfoBO.getMobile()).setSub_pay(1).setComment("小件单【" + smallproId + "】换货订购")
                        .setOpUser(oaUserBO.getUserName()).setSub_mobile(userInfoBO.getMobile())
                        .setArea(smallpro.getToAreaId() == null ? smallpro.getAreaId() : smallpro.getToAreaId())
                        .setDelivery(1).setSubtype(1).setYifuM(0.0).setFeeM_(0.0).setYouhui1M(0.0)
                        .setShouxuM(0.0).setJidianM(0.0).setCoinM(0.0).setSub_check(1).setInuser(oaUserBO.getUserName());
                List<Long> ppidList;
                // 改成备货  置换后的ppid
                if (null != smallpro.getChangePpriceid()) {
                    ppidList = Stream.of(smallpro.getChangePpriceid().longValue()).collect(Collectors.toList());
                } else {
                    ppidList =
                            smallproBillList.stream().map(SmallproBill::getPpriceid).collect(Collectors.toList());
                }
                Map<Long, Integer> ppriceIdCountMap =
                        smallproBillList.stream().collect(Collectors.toMap(SmallproBill::getPpriceid,
                                SmallproBill::getCount));
                List<SmallproOneClickStockingProductInfoBO> productInfoList =
                        baseMapper.getProductInfoByOneClickStocking(ppidList);
                List<SmallproOneClickStockingBasketBO> basketList = null;
                if (productInfoList != null && productInfoList.size() > 0) {
                    basketList = new ArrayList<>(productInfoList.size());
                    for (SmallproOneClickStockingProductInfoBO temp : productInfoList) {
                        SmallproOneClickStockingBasketBO basketTemp = new SmallproOneClickStockingBasketBO();
                        basketTemp.setProduct_name(temp.getProductName())
                                .setProduct_color(temp.getProductColor())
                                .setPpriceid(temp.getPpriceId())
                                .setProduct_num(ppriceIdCountMap.get(temp.getPpriceId().longValue()))
                                .setProduct_price(temp.getMemberPrice().setScale(SCALE, RoundingMode.HALF_UP).doubleValue())
                                .setProduct_price1(temp.getMemberPrice().setScale(SCALE, RoundingMode.HALF_UP).doubleValue())
                                .setProduct_peizhi(temp.getConfig())
                                .setCostprice(temp.getCostPrice().setScale(SCALE, RoundingMode.HALF_UP).doubleValue())
                                .setIsmobile(temp.getIsMobile() ? 1 : 0)
                                .setProductid(temp.getProductId())
                                .setBeihuo(2)
                                .setSeller(oaUserBO.getUserName());
                        basketList.add(basketTemp);
                    }
                }
                if (basketList == null || basketList.size() <= 0) {
                    result.setCode(500);
                    result.setMessage("当前商品列表无符合备货条件的商品！");
                    return result;
                }
                log.warn(smallproId + ":进行一键备货!");
                SmallproNormalCodeMessageRes submitOrderResult =
                        smallproForwardExService.submitOrderByOneClickStocking(subBO, basketList);
                if (submitOrderResult.getCode() == 500) {
                    result.setCode(500);
                    result.setMessage(submitOrderResult.getMessage());
                    return result;
                } else if (submitOrderResult.getCode() == 0) {
                    Integer subId = Integer.valueOf(submitOrderResult.getMessage());
                    smallpro.setHhSubId(subId);
                    Integer flag = baseMapper.updateById(smallpro);
                    if (flag == null || flag <= 0) {
                        log.error("一键备货更新小件单错误！smallproId:" + smallproId + ",hhSubId:" + subId);
                    }
                    StringBuilder commentBuilder = new StringBuilder("小件单【");
                    commentBuilder.append(smallproId).append("】换货无现货库存，生成备货订单号：<a target='_blank' " +
                            "href='/addOrder/editOrder?SubID=")
                            .append(subId).append("'>").append(subId).append("</a>");
                    smallproLogService.addLogs(smallproId, commentBuilder.toString(), oaUserBO.getUserName(), 0);
                    Areainfo areainfo = areainfoService.getByIdSqlServer(smallpro.getAreaId());
                    commentBuilder = new StringBuilder("尊敬的");
                    commentBuilder.append(areainfo.getPrintName()).append("会员您好，您的")
                            .append(smallpro.getName()).append("商品经检测为").append(smallpro.getProblem())
                            .append("故障，已为您调货换新处理，请耐心等待。");
                    smallproLogService.addLogs(smallproId, commentBuilder.toString(), oaUserBO.getUserName(), 1);
                } else {
                    result.setCode(500);
                    result.setMessage("未知错误！");
                    return result;
                }

            } else {
                result.setCode(500);
                result.setMessage("要[处理中]的[换货]小件单才只支持这个操作");
                return result;
            }
        }
        result.setCode(0);
        result.setMessage("一键备货成功！");
        return result;
    }

    // endregion

    // region 根据条形码获取对应的PpriceId getPpriceIdByBarcodeWithSmallpro

    @Override
    public Integer getPpriceIdByBarcodeWithSmallpro(String barCode) {
        Integer result = baseMapper.getPpriceIdByBarcodeWithSmallpro(barCode);
        if (result == null || result <= 0) {
            return null;
        } else {
            return result;
        }
    }

    // endregion

    // region 验证钢化膜是否绑定串号 checkTemperedFilm

    @Override
    public SmallproNormalCodeMessageRes checkTemperedFilm(Integer basketId) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Integer flag = baseMapper.checkTemperedFilm(basketId);
        if (flag != null & flag > 0) {
            result.setCode(0).setMessage("验证成功！");
        } else {
            result.setCode(500).setMessage("钢化膜串号未绑定，请到原始订单进行绑定");
        }
        return result;
    }


    // endregion

    // region


    @Override
    public Boolean addCutScreenReqLog(CutScreenReq req) {
        if (!XtenantEnum.isJiujiXtenant()) {
            return true;
        }
        Integer basketId = req.getBasketId();
        Integer subKinds = req.getSubKinds();
        String inuser = req.getInuser();
        String comment = req.getComment();
        if (CutScreenOrderTypeEnum.SUB.getCode().equals(subKinds)) {
            Basket basketInfo = basketService.getByIdSqlServer(basketId);
            if (Objects.nonNull(basketInfo)) {
                SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
                subLogsNewReq.setComment(comment);
                subLogsNewReq.setSubId(Convert.toInt(basketInfo.getBasketId()));
                subLogsNewReq.setInUser(inuser);
                subLogsNewReq.setShowType(Boolean.TRUE);
                subLogsNewReq.setType(2);
                subLogsNewReq.setDTime(LocalDateTime.now());
                subLogsCloud.addSubLog(subLogsNewReq);
            }
        }
        if (CutScreenOrderTypeEnum.SMALLPRO.getCode().equals(subKinds)) {
            SmallproBill smallproBill = smallproBillService.list(new LambdaQueryWrapper<SmallproBill>().eq(SmallproBill::getId, basketId))
                    .stream().findFirst().orElse(null);
            if (Objects.isNull(smallproBill)) {
                return false;
            }
            smallproLogService.addLogs(smallproBill.getSmallproID(),
                    comment,
                    inuser, 0);
        }
        return true;
    }

    @Override
    public SmallproBasketRes getSmallproBasketList(String subId) {
        SmallproBasketRes res = new SmallproBasketRes();
        List<SmallproBasketRes.SmallproBasket> smallproBasketList = new ArrayList<>();
        res.setSmallproBasketList(smallproBasketList);
        if (StringUtils.isEmpty(subId)) {
            return res;
        }
        List<String> subIdList = Arrays.asList(subId.split(StringPool.COMMA));
        for (String s : subIdList) {
            try {
                Integer.parseInt(s);
            } catch (NumberFormatException e) {
                throw new CustomizeException("请输入正确的单号:"+s);
            }
        }

        //获取销售单明细
        List<Basket> basketList = basketService.lambdaQuery().in(Basket::getSubId, subIdList).orderByAsc(Basket::getSubId).list();
        List<Integer> ppids = basketList.stream().map(x -> Convert.toInt(x.getPpriceid())).distinct().collect(Collectors.toList());
        //获取商品数据
        Map<Integer, Productinfo> productMapByPpids = productinfoService.getProductMapByPpids(ppids);

        Set<String> errorSubIdList = new HashSet<>();
        List<String> correctList = this.baseMapper.checkSub(subIdList);
        for (String s : subIdList) {
            if (!correctList.contains(s)) {
                errorSubIdList.add(s);
            }
        }

        for (Basket basket : basketList) {
            Long ppriceid = basket.getPpriceid();
            Productinfo productinfo = productMapByPpids.get(Convert.toInt(ppriceid));
            if (Objects.isNull(productinfo) || productinfo.getIsmobile1()) {
                continue;
            }
            SmallproBasketRes.SmallproBasket temp = new SmallproBasketRes.SmallproBasket();
            temp.setCount(basket.getBasketCount());
            temp.setPpriceid(ppriceid);
            temp.setProductName(productinfo.getProductName());
            temp.setProductColor(productinfo.getProductColor());
            temp.setReturnPrice(ObjectUtil.defaultIfNull(basket.getPriceShouhou(), basket.getPrice2()));
            temp.setInprice(basket.getInprice());
            temp.setSubId(basket.getSubId());
            temp.setBasketId(basket.getBasketId());
            smallproBasketList.add(temp);
        }
        if (CollectionUtils.isNotEmpty(errorSubIdList)) {
            throw new CustomizeException("仅已完成状态的分销订单和九讯严选订单，且含有小件商品，方可进行批量退货，不满足的单号："
                    + StringUtils.join(errorSubIdList, StringPool.COMMA));
        }
        return res;
    }

    @Override
    public R stockUp(SmallStockUpReq req) {
        ChangeInfoRes.WorkTypeEnum workTypeEnum = EnumUtil.getEnumByCode(ChangeInfoRes.WorkTypeEnum.class, req.getWorkType());
        if(workTypeEnum == null){
            return R.error(StrUtil.format("操作类型[{}]错误", req.getWorkType()));
        }
        CsharpCloud csharpCloud = SpringUtil.getBean(CsharpCloud.class);
        switch (workTypeEnum){
            case STOCK:
                return csharpCloud.peiJianBeiHuoAdd(PeiJianBeiHuoAddReq.builder().act("bei").basketId(req.getSmallproBillId())
                        .basketType(req.getStockType()).count(req.getCount()).ppriceid(req.getPpriceid()).build());
            case CANCEL_STOCK:
                return csharpCloud.peiJianBeiHuoAdd(PeiJianBeiHuoAddReq.builder().act("del").basketId(req.getSmallproBillId())
                        .basketType(req.getStockType()).count(req.getCount()).ppriceid(req.getPpriceid()).build());
            case LOCK:
                return csharpCloud.smallProBeihuo(req.getSmallproBillId());
            case CANCEL_LOCK:
                return csharpCloud.smallProBeihuoCancel(req.getSmallproBillId());
            default:
                return R.error(StrUtil.format("不支持操作类型[{}]", workTypeEnum.getMessage()));
        }
    }

    @Override
    public R addStockLog(SmallproBillLogVo req) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        req.setInUser(oaUser.getUserName());
        req.setLogTime(LocalDateTime.now());
        return SpringUtil.getBean(SmallproBillLogClient.class).addLog(req);
    }

    @Override
    public R<List<SmallproBillLogVo>> listStockLog(Integer billId, Integer type) {
        return SpringUtil.getBean(SmallproBillLogClient.class).listLogByBillId(billId, type);
    }

    @Override
    public SmallproNormalCodeMessageRes appTemperedFilmCode(String imei, Integer smallproId, Integer basketId,
                                                            String fid, Integer kind) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Integer check = baseMapper.checkTemperedFilmWithImei(basketId, imei);
        if (check == null || check <= 0) {
            result.setCode(500).setMessage("串号校验失败，请检查串号是否正确!");
            return result;
        }
        Smallpro smallpro = baseMapper.getByIdSqlServer(smallproId);
        if (smallpro == null) {
            result.setCode(500).setMessage("小件单号错误！");
            return result;
        }
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        smallpro.setCodeMsg(MessageFormat.format("扫码验证[app 扫码验证] 【{0}】 {1}", oaUser.getUserName()
                , LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))).setFid(fid);
        boolean flag = updateById(smallpro);
        if (!flag) {
            result.setCode(500).setMessage("数据更新失败！");
            return result;
        }
        Areainfo areainfo = areainfoService.getByIdSqlServer(smallpro.getAreaId());
//        if(areainfo!=null&&areainfo.getIsSend()&&)

        return result;
    }

    @Override
    public List<Smallpro> checkOrginSubCommitAndUnDeal(List<Integer> basketId) {
        return baseMapper.checkOrginSubCommitAndUnDeal(basketId);
    }

    @Override
    public Integer getSmallProIdBySubId(Integer subId, String comment) {
        return baseMapper.getSmallProIdBySubId(subId, comment);
    }

    @Override
    public SXSSFWorkbook getWorkBook(SmallproReturnGoodsReq query,
                                     String fileName) {
        // 限定查询前三个月的数据
//        LocalDateTime date = LocalDateTime.now().plusMonths(-3L).with(TemporalAdjusters.firstDayOfMonth());
//        LocalDateTime date = LocalDateTime.now();
//        String format = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//        query.setStartExcelTime(format);
        SmallproServiceImpl smallproService = (SmallproServiceImpl) AopContext.currentProxy();
        List<SmallproReturnGoodsRes> list = new LinkedList<>();
        //分批查询
        for (int i = 1; i <= EXPORT_PAGES; i++) {
            R<PageVo> smallproPage = smallproService.getSmallproPage(query, EXPORT_PAGE_SIZE, i, false);
            if (smallproPage.getData().getRecords() != null) {
                list.addAll((List<SmallproReturnGoodsRes>) smallproPage.getData().getRecords());

            }
            if (smallproPage.getData().getRecords() == null || list.size() < i * EXPORT_PAGE_SIZE) {
                //没有数据退出查询
                break;
            }
        }


        // excel标题
        String[] title = {"维修编号","原订单", "接件时间", "当前所在地", "接件大区", "接件地区", "保修", "商品名称","接件商品ppid","置换商品名称","置换商品ppid", "处理方式", "使用服务","费用", "成本", "分组", "接件人", "状态", "故障描述", "userId"};
        // excel文件名
        // sheet名
        String sheetName = "小件管理列表";
        int size = list.size();
        List<Integer> areaIds = list.stream().map(SmallproReturnGoodsRes::getAreaId).distinct().collect(Collectors.toList());
        BigAreaQueryReq req = new BigAreaQueryReq();
        req.setDataType(3).setAreaIds(areaIds);
        R<List<BigAreaInfoRes>> bigAreaInfoListR = areaInfoClient.getBigAreaInfoList(req);

        String[][] content = new String[size + 2][10];
        DecimalFormat df = new DecimalFormat("0.00");
        for (int i = 0; i < size; i++) {
            content[i] = new String[title.length];
            SmallproReturnGoodsRes bo = list.get(i);
            content[i][0] = bo.getId().toString();
            content[i][1] = bo.getSubId()+"";
            content[i][2] = Optional.ofNullable(bo.getInDate()).orElse("");
            content[i][3] = bo.getToArea();
            BigAreaInfoRes bigAreaInfoRes = bigAreaInfoListR.getData().stream().filter(item -> Objects.equals(bo.getAreaId(), item.getAreaId())).findFirst().orElse(null);
            content[i][4] = bo.getArea();
            if (bigAreaInfoRes != null) {
                content[i][4] = bigAreaInfoRes.getDepartName();
            }
            content[i][5] = bo.getArea();
            content[i][6] = bo.getWarrantyStatus() ? "在保" : "非保";
            content[i][7] = bo.getProductName();
            content[i][8] = bo.getPpriceid()+"";
            content[i][9] = bo.getChangePpriceidName();
            content[i][10] = ObjectUtil.isNotNull(bo.getChangePpriceid())?bo.getChangePpriceid().toString():"";
            content[i][11] = EnumUtil.getMessageByCode(SmallProKindEnum.class, bo.getKind());
            content[i][12] = bo.getServiceTypeValue();
            content[i][13] = df.format(Optional.ofNullable(bo.getMaintainPrice()).orElse(BigDecimal.ZERO)) + "";
            content[i][14] = df.format(Optional.ofNullable(bo.getCostPrice()).orElse(BigDecimal.ZERO)) + "";
            content[i][15] = bo.getGroupName();
            content[i][16] = bo.getInUser();
            content[i][17] = EnumUtil.getMessageByCode(SmallProStatsEnum.class, bo.getStatus());
            content[i][18] = bo.getProblem();
            content[i][19] = String.valueOf(bo.getUserId());
        }
        // 创建HSSFWorkbook
        return ExcelUtils.getSXSSFWorkbook(sheetName, title, content);
    }

    // endregion

    // endregion

    // region transactional

    @Transactional(rollbackFor = Exception.class)
    public boolean toAreaWrite(SmallproNormalCodeMessageRes result, Integer smallproId, Integer areaId,
                               Integer toAreaId, Integer type,
                               OaUserBO oaUserBO, Areainfo senderInfo, Areainfo receiveInfo,
                               LogisticsRecipientBO receiver, Boolean createWuliu) {
        Smallpro smallpro = baseMapper.getByIdSqlServer(smallproId);
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallproId);
        List<SmallproBill> smallproBillList =
                smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        Integer count = 0;
        for (SmallproBill temp : smallproBillList) {
            count += temp.getCount();
        }
        boolean updateFlag = false;
        if (type == 1) {
            // kind=2/4 stats!=0
            if ((smallpro.getKind() == 2 || smallpro.getKind() == 4) && smallpro.getStats() == 0) {
                result.setMessage("操作失败！换货与现货转地区请先取件！");
                result.setCode(500);
                return false;
            }
            Integer oldAreaId = smallpro.getAreaId();
            if (!smallpro.getIsToArea()) {
//                if (oldAreaId.equals(toAreaId)) {
//                    smallpro.setAreaId(smallpro.getToAreaId());
//                }
                smallpro.setToAreaId(toAreaId).setIsToArea(true).setToAreaTime(LocalDateTime.now());
                updateFlag = ((SmallproServiceImpl) AopContext.currentProxy()).updateById(smallpro);
            }
            List<Integer> toAreaList = new ArrayList<>(4);
            List<Integer> areaList = new ArrayList<>(4);
            AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(smallpro.getAreaId());
            toAreaList.add(backEndInfo.getDcAreaId());
            toAreaList.add(backEndInfo.getH1AreaId());
            toAreaList.add(JiujiAreaIdEnum.JIUJI_AREA_ID_DC1.getCode());
            toAreaList.add(backEndInfo.getD1AreaId());
            toAreaList.add(JiujiAreaIdEnum.JIUJI_AREA_ID_H3.getCode());
            areaList.add(backEndInfo.getDcAreaId());
            areaList.add(backEndInfo.getH1AreaId());
            areaList.add(JiujiAreaIdEnum.JIUJI_AREA_ID_DC1.getCode());
            areaList.add(backEndInfo.getD1AreaId());
            areaList.add(JiujiAreaIdEnum.JIUJI_AREA_ID_H3.getCode());
            // 李飞说：2020.04.24 所有转地区自动生成物流单
            // 判断是否自动生成物流单
            // 现货换货取件自动转地区到d1,并自动生成物流单
            if ((toAreaList.contains(toAreaId) && smallpro.getKind() == 1)
                    || areaList.contains(areaId)) {
//                    || (smallpro.getKind() == 2 && toAreaId.equals(JiujiAreaIdEnum.JIUJI_AREA_ID_D1.getCode()))
//                    || (smallpro.getKind() == 4 && toAreaId.equals(JiujiAreaIdEnum.JIUJI_AREA_ID_D1.getCode()))) {
//            if (createWuliu) {
                SubWLModelReq subWLModelReq = buildSmallproWuliuReqForToArea(senderInfo, receiveInfo, oaUserBO, count,
                        smallpro, receiver);
                R<Integer> wuliuResult = wuliuCloud.saveWuLiu(subWLModelReq, areaId, oaUserBO.getUserName());
                Integer wuliuId = 0;
                if (wuliuResult.getCode() == 0) {
                    wuliuId = wuliuResult.getData();
                    smallpro.setWuliuId(wuliuId);
                    updateFlag = ((SmallproServiceImpl) AopContext.currentProxy()).updateById(smallpro);
                    StringBuilder comment = new StringBuilder("小件由");
                    comment.append(senderInfo.getArea());
                    comment.append("发往");
                    comment.append(receiveInfo.getArea());
                    comment.append("已自动生成物流单，物流单号<a href='/addOrder/wuliu?wuliuid=");
                    comment.append(wuliuId);
                    comment.append("'>");
                    comment.append(wuliuId);
                    comment.append("</a>");
                    smallproLogService.addLogs(smallproId, comment.toString(), oaUserBO.getUserName(), 1);

                    //物流时效计算
                    sendWuliuProcess(smallpro, oaUserBO.getUserName(), type);
                } else {
                    result.setCode(500);
                    result.setMessage("生成物流单失败！");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return false;
                }
            }
        } else {
            if (smallpro.getIsToArea()) {
                UpdateWrapper<Smallpro> smallproUpdateWrapper = new UpdateWrapper<>();
                smallproUpdateWrapper.lambda().set(Smallpro::getIsToArea, false).eq(Smallpro::getId, smallproId);
                updateFlag = ((SmallproServiceImpl) AopContext.currentProxy()).update(smallproUpdateWrapper);
                if (!updateFlag) {
                    result.setCode(500);
                    result.setMessage("接收更新数据库错误！");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return false;
                }
            }
        }

        if (updateFlag) {
            if (senderInfo != null && receiveInfo != null) {
                String areaName = senderInfo.getAreaName() + "(" + senderInfo.getArea() + ")";
                String toAreaName = receiveInfo.getAreaName() + "(" + receiveInfo.getArea() + ")";
                AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(smallpro.getAreaId());
                if (toAreaId.equals(backEndInfo.getDcAreaId()) || toAreaId.equals(backEndInfo.getH1AreaId())) {
                    toAreaName = receiveInfo.getPrintName();
                } else if (areaId.equals(backEndInfo.getDcAreaId()) || areaId.equals(backEndInfo.getH1AreaId())) {
                    areaName = receiveInfo.getPrintName();
                }
                ShouhouMsgconfig shouhouMsgconfig;
                String format = "";
                if (type == 1) {
                    shouhouMsgconfig =
                            shouhouMsgconfigService.getShouhouMsgconfigByLogType(ShouHouMsgConfigEnum.TYPE6.getCode());
                    if (null != shouhouMsgconfig) {
                        format = MessageFormat.format(shouhouMsgconfig.getMsgcontent(), areaName, toAreaName);
                    }
                } else {
                    shouhouMsgconfig =
                            shouhouMsgconfigService.getShouhouMsgconfigByLogType(ShouHouMsgConfigEnum.TYPE9.getCode());
                    if (null != shouhouMsgconfig) {
                        format = MessageFormat.format(shouhouMsgconfig.getMsgcontent(), areaName, toAreaName);
                    }
                }
                if (StringUtils.isNotEmpty(format)) {
                    // 保存日志，发送微信消息
                    sendSmallProWeiXinMsg(smallpro.getUserId(), areaId, shouhouMsgconfig, format, smallpro.getMobile(),
                            smallpro.getId());
                    smallproLogService.addLogs(smallpro.getId(), format, oaUserBO.getUserName(), 1);
                }
            }
            if (type != 1) {
                if (smallpro.getWuliuId() != null) {
                    //查询小件单关联该物流单， 的小件单是否全部完成
                    R<Boolean> wuliuFlag = wuliuCloud.smallproAutoComplete(smallpro.getWuliuId(), smallproId,
                            oaUserBO.getUserName());

                    //物流时效计算
                    sendWuliuProcess(smallpro, oaUserBO.getUserName(), type);
                }
            }
        }
        return true;
    }

    /**
     * 发送物流时效节点消息
     *
     * @param smallpro
     * @param userName
     * @param type
     */
    private void sendWuliuProcess(Smallpro smallpro, String userName, Integer type) {
        if (!XtenantEnum.isJiujiXtenant()) {
            return;
        }
        String pushMsg = JSON.toJSONString(new WuliuProcessBO()
                .setAct("wuliu_process_event").setData(new WuliuProcessBO.DataBO().setBusinessId(Convert.toStr(smallpro.getId()))
                        .setBusinessNode(Objects.equals(1, type) ? SubDynamicsBusinessNodeEnum.AFTERSALES_PARTS_TRANSFER_CREATE.getCode() : SubDynamicsBusinessNodeEnum.AFTERSALES_PARTS_TRANSFER_RECEVIED.getCode())
                        .setBusinessType(ProcessBusinessTypeEnum.AFTER_SALES_PARTS.getCode())
                        .setOperationTime(LocalDateTime.now())
                        .setXtentant(XtenantEnum.getXtenant().longValue())
                        .setEmployeeName(userName)
                        .setSource("after:toAreaWrite")
                        .setStatus(0)
                        .setDelayMillisecond(0)
                        .setAreaId(0)
                        .setNextAreaId(0)
                        .setRelatedId(smallpro.getWuliuId())
                ));
        oaAsyncRabbitTemplate.convertAndSend("wuliu_process", pushMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateSmallproRepairStatusWrite(SmallproNormalCodeMessageRes result, Smallpro smallpro) {
        boolean updateFlag = ((SmallproServiceImpl) AopContext.currentProxy()).updateById(smallpro);
        if (!updateFlag) {
            result.setCode(500);
            result.setMessage("小件接件数据库更新失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }


    // endregion

    // region 数据库操作方法

    // region 读权限数据库账户

    @Override
    public Smallpro getByIdSqlServer(Integer id) {
        return SpringContextUtil.reqCache(() -> baseMapper.getByIdSqlServer(id), RequestCacheKeys.SMALLPRO_GET_BY_ID_SQL_SERVER, id);
    }

    @Override
    public List<Smallpro> listSqlServer(Wrapper wrapper) {
        return baseMapper.listSqlServer(wrapper);
    }

    // endregion

    // endregion

    /**
     * 添加kindType
     *
     * @param smallproId
     * @param kindTypeList
     */
    private void addSmallproKindtype(Integer smallproId, List<Integer> kindTypeList) {
        if (CollectionUtils.isEmpty(kindTypeList)) {
            return;
        }
        //执行删除类别操作
        smallproKindtypeService.remove(new LambdaQueryWrapper<SmallproKindtype>().eq(SmallproKindtype::getSmallproid,
                smallproId));
        List<SmallproKindtype> smallproKindtypeList = kindTypeList.stream().map(e -> {
            SmallproKindtype smallproKindtype = new SmallproKindtype();
            smallproKindtype.setKind(e);
            smallproKindtype.setSmallproid(smallproId);
            return smallproKindtype;
        }).collect(Collectors.toList());
        for (SmallproKindtype temp : smallproKindtypeList) {
            smallproKindtypeService.save(temp);
        }
    }


    /**
     * description: <组装选择项>
     *
     * @param enumClass 枚举类Class对象
     * @param key       选择项对应的字段Key
     * @return void
     * <AUTHOR>
     * @date 15:08 2019/11/25
     * @since 1.0.0
     **/
    private <T extends CodeMessageEnumInterface> void buildUpOption(
            Class<T> enumClass, String key, HashMap<String, Object> optionResult) {
        HashMap<String, Object> mapTemp;
        CodeMessageEnumInterface[] enumArray = enumClass.getEnumConstants();
        optionResult.put("key", key);
        List<HashMap<String, Object>> optionList = new ArrayList<>(enumArray.length);
        for (CodeMessageEnumInterface enumTemp : enumArray) {
            if (enumTemp.getCode() != null) {
                mapTemp = new HashMap<>(2);
                mapTemp.put("label", enumTemp.getMessage());
                mapTemp.put("value", enumTemp.getCode());
                optionList.add(mapTemp);
            }
        }
        optionResult.put("option", optionList);
    }

    /**
     * description: <组装选择项>
     * translation: <Assembly option>
     *
     * @param enumClass 枚举类Class对象
     * @param key       选择项对应的字段Key
     * @return void
     * <AUTHOR>
     * @date 15:08 2019/11/25
     * @since 1.0.0
     **/
    private <T extends CodeMessageEnumInterface> void buildUpOption(
            Class<T> enumClass, String key, HashMap<String, Object> optionResult, List<Object> codes) {
        HashMap<String, Object> mapTemp;
        CodeMessageEnumInterface[] enumArray = enumClass.getEnumConstants();
        optionResult.put("key", key);
        List<HashMap<String, Object>> optionList = new ArrayList<>(enumArray.length);
        for (CodeMessageEnumInterface enumTemp : enumArray) {
            if (enumTemp.getCode() != null && codes.contains(enumTemp.getCode())) {
                mapTemp = new HashMap<>(2);
                mapTemp.put("label", enumTemp.getMessage());
                mapTemp.put("value", enumTemp.getCode());
                optionList.add(mapTemp);
            }
        }
        optionResult.put("option", optionList);
    }


    /**
     * description: <组装带分类选择项>
     * translation: <Assembly tape classification option>
     *
     * @param enumClass  枚举类Class对象
     * @param flagKeyMap 分类对应Key的Map
     * @param flagMap    分类对应结果Map的Map
     * @return void
     * <AUTHOR>
     * @date 17:35 2019/11/25
     * @since 1.0.0
     **/
    private <T extends CodeMessageFlagEnumInterface> void buildUpOptionByFlag(
            Class<T> enumClass, HashMap<Integer, String> flagKeyMap,
            HashMap<Integer, HashMap<String, Object>> flagMap) {
        CodeMessageFlagEnumInterface[] enumArray = enumClass.getEnumConstants();
        for (Integer flagTemp : flagMap.keySet()) {
            flagMap.get(flagTemp).put("option", new ArrayList<HashMap<String, Object>>(enumArray.length));
            flagMap.get(flagTemp).put("key", flagKeyMap.get(flagTemp));
        }
        HashMap<String, Object> mapTemp;
        ArrayList<HashMap<String, Object>> listTemp;
        for (CodeMessageFlagEnumInterface enumTemp : enumArray) {
            if (enumTemp.getCode() != null) {
                mapTemp = new HashMap<>(2);
                mapTemp.put("label", enumTemp.getMessage());
                mapTemp.put("value", enumTemp.getCode());
                listTemp = (ArrayList<HashMap<String, Object>>) flagMap.get(enumTemp.getFlag()).get("option");
                listTemp.add(mapTemp);
            }
        }
    }


    private SubWLModelReq buildSmallproWuliuReqForToArea(Areainfo sendInfo, Areainfo receiveInfo, OaUserBO oaUserBO,
                                                         Integer count, Smallpro smallpro,
                                                         LogisticsRecipientBO receiver) {
        String toAreaName = receiveInfo.getAreaName() + "(" + receiveInfo.getArea() + ")";
        String toAreaFlag = toAreaName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        SubWLModelReq req = new SubWLModelReq();

        if (count != 0) {
            StringBuilder wuliuContentBuilder = new StringBuilder("小件调拨，共计");
            wuliuContentBuilder.append(count).append("台,转地区标识码：").append(toAreaFlag)
                    .append(",单号：").append(smallpro.getId());
            String comment = wuliuContentBuilder.toString();
            req.setAreaId(sendInfo.getId());
            req.setActionName("小件调拨");
            req.setReceiveAddress(receiveInfo.getCompanyAddress());

            AreaBelongsDcHqD1AreaId areaBelongsDcHqD1AreaId = areainfoService.getAreaBelongsDcHqD1AreaId(receiveInfo.getId());
            if (areaBelongsDcHqD1AreaId != null) {
                if (Objects.equals(areaBelongsDcHqD1AreaId.getH1AreaId(), receiveInfo.getId())
                        || Objects.equals(areaBelongsDcHqD1AreaId.getDcAreaId(), receiveInfo.getId())) {
                    Areainfo area = areainfoService.getById(areaBelongsDcHqD1AreaId.getH1AreaId());
                    req.setReceiveAddress(area.getCompanyAddress());
                }
            }
            //这里 后端总部id写死，需要适配输出
//            if (receiveInfo.getId().equals(JiujiAreaIdEnum.JIUJI_AREA_ID_H1.getCode()) || receiveInfo.getId().equals(JiujiAreaIdEnum.JIUJI_AREA_ID_DC.getCode())) {
//                req.setReceiveAddress("云南省昆明市五华区学府路690号金鼎科技园三号楼");
//            }
            ((SmallproServiceImpl) AopContext.currentProxy()).buildCommonSmallProWuLiu(sendInfo, receiveInfo, oaUserBO
                    , receiver, req, comment);
        }
        return req;
    }

    @Override
    public void buildCommonSmallProWuLiu(Areainfo sendInfo, Areainfo receiveInfo, OaUserBO oaUserBO,
                                         LogisticsRecipientBO receiver, SubWLModelReq req, String comment) {
        if (comment.length() > 190) {
            comment = comment.substring(0, 190);
        }
        req.setComment(comment);
        List<String> userNameList = new ArrayList<>(2);
        String sendUserMobile = "";
        String receiverUserMobile = "";
        userNameList.add(oaUserBO.getUserName());
        if (null != receiver && null != receiver.getReceiveUserName()) {
            userNameList.add(receiver.getReceiveUserName());
        }
        List<SmallproInfoInuserInfoBO> userInfoList = baseMapper.getSmallproInUserInfo(userNameList);
        for (SmallproInfoInuserInfoBO bo : userInfoList) {
            if (oaUserBO.getUserName().equals(bo.getInUserName())) {
                sendUserMobile = bo.getMobile();
            } else if (null != receiver && null != receiver.getReceiveUserName()
                    && receiver.getReceiveUserName().equals(bo.getInUserName())) {
                receiverUserMobile = bo.getMobile();
            }
        }
        req.setSendName(oaUserBO.getUserName());
        req.setSendMobile(sendUserMobile);
        req.setSendAddress(sendInfo.getCompanyAddress());
        req.setSAreaId(sendInfo.getId());
        req.setSendCityId(sendInfo.getCityid());
        if (null != receiver) {
            req.setReceiveName(receiver.getReceiveUserName());
        }
        req.setRAreaId(receiveInfo.getId());
        req.setReceiveCityId(receiveInfo.getCityid());
        req.setConsignee(receiveInfo.getAreaName());
        req.setStatus(1);
        req.setReceiveMobile(receiverUserMobile);
        req.setDispatchedPerson("");
        req.setLogisticType(1);
        req.setDTime(LocalDateTime.now());
        req.setSpid(sendInfo.getPid());
        req.setSzid(sendInfo.getZid());
        req.setSdid(sendInfo.getDid());
        req.setRpid(receiveInfo.getPid());
        req.setRzid(receiveInfo.getZid());
        req.setRdid(receiveInfo.getDid());
        req.setInUser(oaUserBO.getUserName());
        req.setPrice(BigDecimal.ZERO);
        req.setCostPrice(BigDecimal.ZERO);
        req.setCom("");
        req.setLogs(new ArrayList<>());
        req.setWCateId(WuliuCateEnum.SHXJDB.getCode());
    }

    @Override
    public Boolean addSmallproLogBatch(SmallproAddLogBatchReq req, String userName) {
        String comment = req.getComment();
        List<Integer> idList = req.getIdList();
        List<AddSmallproLogBatchBO> addSmallproLogBatchBOS = baseMapper.getAddSmallproLogBatch(idList);
        Map<Integer, List<AddSmallproLogBatchBO>> listMap =
                addSmallproLogBatchBOS.stream().collect(Collectors.groupingBy(AddSmallproLogBatchBO::getId));
        listMap.entrySet().forEach(e -> {
            Integer smallproId = e.getKey();
            List<AddSmallproLogBatchBO> value = e.getValue();
            String inuser = value.get(0).getInuser();
            String productName =
                    value.stream().map(AddSmallproLogBatchBO::getProductName).collect(Collectors.joining(","));
            SmallproAddLogReq smallproAddLogReq = new SmallproAddLogReq();
            smallproAddLogReq.setSmallproId(smallproId);
            smallproAddLogReq.setProductName(productName);
            smallproAddLogReq.setToWeixin(1);
            smallproAddLogReq.setToEmail(0);
            smallproAddLogReq.setToSms(0);
            smallproAddLogReq.setShowType(0);
            smallproAddLogReq.setComment(comment);
            smallproAddLogReq.setUserName(inuser);
            addSmallproLogWithPush(smallproAddLogReq, userName);
        });
        return true;
    }

    @Override
    public Integer getSpotSubData(Integer subId, List<Long> ppIds) {
        return baseMapper.getSpotSubData(subId, ppIds);
    }

    @Override
    public void saveSmallproAttachments(List<MiniFileRes> filesList, OaUserBO oaUserBO, Integer smallproId) {
        if (filesList != null && filesList.size() > 0 && null != smallproId) {
            int type = NumberConstant.THIRTY + NumberConstant.EIGHT;
            //查询存在库里的文件
            List<Attachments> dbAttachments = attachmentsService.getAttachmentsByLinkId(smallproId, AttachmentsEnum.SMALL_ITEMS.getCode(),null);
            List<FileReq> fileReqs = filesList.stream().map(fileInfo -> {
                FileReq fr = new FileReq();
                fr.setFid(fileInfo.getFid());
                fr.setFileName(fileInfo.getFileName());
                fr.setFilePath(fileInfo.getFilePath());
                fr.setFileSize((double) fileInfo.getFileSize());
                dbAttachments.stream().filter(da -> Objects.equals(da.getFid(), fr.getFid())).findFirst().ifPresent(fr::setAttachments);
                return fr;
            }).collect(Collectors.toList());
            attachmentsService.saveOrUpdate(fileReqs, smallproId, AttachmentsEnum.SMALL_ITEMS.getCode(), oaUserBO.getUserId(), 0, null);
        }
    }

    public void saveBigImeiAttachments(MiniFileRes fileRes, OaUserBO oaUserBO, Integer smallproId) {
        if (ObjectUtil.isNotNull(fileRes)) {
            //查询存在库里的文件
            FileReq fr = new FileReq();
            fr.setFid(fileRes.getFid());
            fr.setFileName(fileRes.getFileName());
            fr.setFilePath(fileRes.getFilePath());
            fr.setFileSize(Convert.toDouble(fileRes.getFileSize()));
            attachmentsService.saveOrUpdate(Collections.singletonList(fr), smallproId, AttachmentsEnum.BIG_IMEI_ATTACHMENTS.getCode(), oaUserBO.getUserId(), 0, null);
        }
    }

    @Override
    public boolean sendWeixinMsg(String wxMsg, String url, String handleType, String status,
                                 Integer areaId, Integer userId, String logs, Integer pushType, String mobile) {
        if (areaId == null || StringUtils.isEmpty(handleType)
                || StringUtils.isEmpty(status)
                || StringUtils.isEmpty(logs)
                || null == userId) {
            return false;
        }
        R<AreaInfo> nowAreaInfoR = areaInfoClient.getAreaInfoById(areaId);
        if (nowAreaInfoR.getCode() != ResultCode.SUCCESS && nowAreaInfoR.getData() != null) {
            return false;
        }
        AreaInfo are = nowAreaInfoR.getData();
        Long xtenant = Long.valueOf(are.getXtenant() == null ? 0 : are.getXtenant());
        String urlPre = commonService.getUrlByXtenant(xtenant, ExtenAntUrlTypeEnum.MURL.getCode());
        if (StringUtils.isBlank(are.getPrintName()) || are.getPrintName().contains("中邮")
                || are.getIsSend() == null || !are.getIsSend()) {
            return false;
        }
        if (are.getIsSend() && (ShouHouMsgConfigPushTypeEnum.TYPE2.getCode().equals(pushType) ||
                ShouHouMsgConfigPushTypeEnum.TYPE3.getCode().equals(pushType))) {
            WeixinUser wxInfo = weixinUserService.getWxxinUserByUserId(userId);
            if (wxInfo != null && StringUtils.isNotBlank(wxInfo.getOpenid())) {
                Result<String> result = imCloud.sendAfterServiceProgressMsg(wxInfo.getOpenid(), urlPre + url, wxMsg,
                        handleType, status, DateUtil.localDateTimeToString(LocalDateTime.now()), logs, "",
                        are.getCityId(), xtenant);
                return result.getCode() == ResultCode.SUCCESS;
            } else if (ShouHouMsgConfigPushTypeEnum.TYPE3.getCode().equals(pushType) && StringUtils.isNotEmpty(mobile)) {
                R<Boolean> smsSendRes = smsService.sendSms(mobile, wxMsg,
                        DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
                return smsSendRes.getCode() == ResultCode.SUCCESS;
            }
        }
        return false;
    }

    @Override
    public SmallReceiptPrintingRes receiptPrinting(Integer smallProId) {
        SmallReceiptPrintingRes res = new SmallReceiptPrintingRes();
        List<SmallReceiptPrintingBO> bos = baseMapper.getReceiptPrinting(smallProId);
        if (CollectionUtils.isEmpty(bos)) {
            return res;
        }
        List<String> names = bos.stream().map(SmallReceiptPrintingBO::getProductName).collect(Collectors.toList());
        SmallReceiptPrintingBO bo = bos.get(0);
        BeanUtils.copyProperties(bo, res);
        res.setBaskets(names);
        res.setSendRepairTime(Optional.ofNullable(bo.getSendRepairTime())
                .map(e -> e.format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"))).orElse(""));
        res.setQuJiTime(Optional.ofNullable(bo.getQuJiTime())
                .map(e -> e.format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"))).orElse(""));
        res.setProcessingKind(EnumUtil.getMessageByCode(SmallProKindEnum.class, bo.getKind()));
        return res;
    }

    @Override
    public R<CheckSmallProWuLiuRes> checkSmallProWuLiu(Integer smallProId, OaUserBO oaUserBO) {
        CheckSmallProWuLiuBO bo = baseMapper.getCheckSmallProWuLiu(smallProId);
        if (bo.getIsToArea() == null || !bo.getIsToArea() ||
                null == oaUserBO.getAreaId() || !oaUserBO.getAreaId().equals(bo.getAreaId())) {
            return R.error("未转地区操作或转出地区与后台地区不一致");
        }
        CheckSmallProWuLiuRes wuLiuRes = new CheckSmallProWuLiuRes();
        wuLiuRes.setFromArea(bo.getArea());
        wuLiuRes.setToArea(bo.getToArea());
        wuLiuRes.setCount(bo.getCount());
        wuLiuRes.setSmallProId(bo.getId());
        wuLiuRes.setProductName(bo.getName());
        return R.success(wuLiuRes);
    }

    @Override
    public CommonTitleRes getTitle() {
        CommonTitleRes commonTitleRes = new CommonTitleRes();
        commonTitleRes.setTitle("小件品类统计");
        ListBean listBean = new ListBean("statistics", "统计项", "select", true,
                enumToOptionList(SmallCategoryStatisticsEnum.class));
        List<String> collect = listBean.getOptions().stream().map(ListBean.OptionsBean::getValue).map(String::valueOf).collect(Collectors.toList());
        listBean.setValue(collect);
        Boolean falseFlag = false;
        ListBean areaBean = new ListBean("areaLevel", "门店级别", ListBean.ListType.SELECT, true,
                enumToOptionList(AreaLevelEnum.class));
        if(XtenantJudgeUtil.isJiujiMore()){
            areaBean = new ListBean("areaLevel", "门店级别", ListBean.ListType.SELECT, true,
                    enumToOptionList(AreaGradeEnum.class));
        }
        commonTitleRes.setList(Stream.of(
                listBean,
                new ListBean("ciDs", "分类", "smallKind", true, null),
                new ListBean("brandIds", "品牌", "brand", true, null),
                new ListBean("areaIds", "地区", "area", true, null),
                new ListBean("administrativeAreaIds", "行政区域", "administrativeArea", true, null),
                areaBean,
                new ListBean("displayLevel", "展陈级别", ListBean.ListType.SELECT, true,
                        enumToOptionList(DisplayLevelEnum.class)),
                new ListBean("areaKind", "门店类别", ListBean.ListType.SELECT, false,
                        enumToOptionList(StoreCategoryEnum.class)),
                new ListBean("display", "上架状态", ListBean.ListType.SELECT, false,
                        enumToOptionList(DisplayEnum.class)),
                new ListBean("timeType", "时间类型", "selectDate", false,
                        enumToOptionList(SmallCategoryTimeTypeEnum.class), "1"),
                new ListBean("bigCategory", "仅大分类", "CheckBox", falseFlag,
                        null, falseFlag),
                new ListBean("noStock", "不含负库存", "CheckBox", falseFlag,
                        null, falseFlag)

        ).collect(Collectors.toList()));
        return commonTitleRes;
    }

    /**
     * 枚举转 OptionsBean 列表
     *
     * @param enumClass 枚举类
     * @return List<ListBean.OptionsBean>
     */
    private static <E extends CodeMessageEnumInterface> List<ListBean.OptionsBean> enumToOptionList(Class<E> enumClass) {
        return Arrays.stream(enumClass.getEnumConstants()).map(item -> {
            ListBean.OptionsBean optionsBean = new ListBean.OptionsBean();
            optionsBean.setValue(String.valueOf(item.getCode()));
            optionsBean.setLabel(item.getMessage());
            return optionsBean;
        }).collect(Collectors.toList());
    }

    @Override
    public CommonDataRes data(SmallCategoryReq req) {
        // 限制时间范围
        limitTime(req);
        List<Integer> areaIds = getAreaIds(req.getAreaIds(), req.getAdministrativeAreaIds());
        //查询本期数据
        CommonDataRes smallCategoryRes = getCommonDataRes(req, areaIds);
        //查询上期数据
        Duration dur = Duration.between(req.getStartTime(), req.getEndTime());
        long l = dur.toDays();
        long endDay = 1L;
        LocalDateTime startTime = req.getStartTime().minusDays(l + endDay);
        LocalDateTime endTime = req.getEndTime().minusDays(l + endDay);
        req.setStartTime(startTime);
        req.setEndTime(endTime);
        return getCommonDataRes(smallCategoryRes, req, areaIds);
    }

    private CommonDataRes getCommonDataRes(CommonDataRes smallCategoryRes, SmallCategoryReq req, List<Integer> areaIds) {
        // 查询销售额，利润额等
        List<SmallCategoryBO> res = baseMapper.getSmallCategoryData(req, areaIds, XtenantEnum.getXtenant());

        if (Boolean.TRUE.equals(req.getBigCategory())) {
            List<CategoryBO> categorys = baseMapper.getAllCategory();
            Map<Integer, Integer> categoryMap = categorys.stream().collect(Collectors.toMap(CategoryBO::getCid, (CategoryBO e) -> {
                String path = e.getPath();
                if (StringUtils.isEmpty(path)) {
                    return e.getCid();
                }
                return Arrays.stream(path.split(",")).filter(StringUtils::isNotEmpty).findFirst().map(Integer::valueOf).orElseGet(e::getCid);
            }));
            Map<Integer, List<SmallCategoryBO>> resMap = res.stream()
                    .map((SmallCategoryBO e) -> {
                        e.setPid(categoryMap.get(e.getCid()));
                        return e;
                    }).collect(Collectors.groupingBy(SmallCategoryBO::getPid));
            return wrapperRes3(smallCategoryRes, resMap, SCALE);
        } else {
            Map<Integer, SmallCategoryBO> resMap = res.stream()
                    .collect(Collectors.toMap(SmallCategoryBO::getCid, Function.identity(), (k1, k2) -> k1));
            return wrapperRes2(smallCategoryRes, resMap);
        }
    }

    private CommonDataRes getCommonDataRes(SmallCategoryReq req, List<Integer> areaIds) {
        // 查询销售额，利润额等
        List<SmallCategoryBO> res = baseMapper.getSmallCategoryData(req, areaIds, XtenantEnum.getXtenant());
        // 查询在途库存
        List<SmallCategoryInventoryBO> inventoryList = baseMapper.getSmallCategoryDataInventory(req, areaIds);
        // 查询库存
        List<SmallCategoryInventoryBO> inventoryOnList = baseMapper.getSmallCategoryDataInventoryOn(req, areaIds);
        //查询陈列和优品数据
        //List<DisplayProductInfoBO> displayProductInfoList = baseMapper.getDisplayProductInfoData(req, areaIds);
        List<DisplayProductInfoBO> displayProductInfoList = new ArrayList<>();

        if (Boolean.TRUE.equals(req.getBigCategory())) {
            List<CategoryBO> categorys = baseMapper.getAllCategory();
            Map<Integer, Integer> categoryMap = categorys.stream().collect(Collectors.toMap(CategoryBO::getCid, (CategoryBO e) -> {
                String path = e.getPath();
                if (StringUtils.isEmpty(path)) {
                    return e.getCid();
                }
                return Arrays.stream(path.split(",")).filter(StringUtils::isNotEmpty).findFirst().map(Integer::valueOf).orElseGet(e::getCid);
            }));
            Map<Integer, List<SmallCategoryBO>> resMap = res.stream()
                    .map((SmallCategoryBO e) -> {
                        e.setPid(categoryMap.get(e.getCid()));
                        return e;
                    }).collect(Collectors.groupingBy(SmallCategoryBO::getPid));
            Map<Integer, List<DisplayProductInfoBO>> displayMap = displayProductInfoList.stream()
                    .map((DisplayProductInfoBO e) -> {
                                e.setPid(categoryMap.get(e.getCid()));
                                return e;
                            }
                    ).collect(Collectors.groupingBy(DisplayProductInfoBO::getPid));
            Map<Integer, List<SmallCategoryInventoryBO>> inventoryMap = inventoryList.stream()
                    .map((SmallCategoryInventoryBO e) -> {
                        e.setPid(categoryMap.get(e.getCid()));
                        return e;
                    }).collect(Collectors.groupingBy(SmallCategoryInventoryBO::getPid));
            Map<Integer, List<SmallCategoryInventoryBO>> inventoryOnMap = inventoryOnList.stream()
                    .map((SmallCategoryInventoryBO e) -> {
                        e.setPid(categoryMap.get(e.getCid()));
                        return e;
                    }).collect(Collectors.groupingBy(SmallCategoryInventoryBO::getPid));
            Map<Integer, String> categoryNameMap = categorys.stream()
                    .collect(Collectors.toMap(CategoryBO::getCid, CategoryBO::getName));
            return wrapperRes4(resMap, inventoryMap, inventoryOnMap, req, categoryNameMap, displayMap);

        } else {
            Map<Integer, SmallCategoryBO> resMap = res.stream()
                    .collect(Collectors.toMap(SmallCategoryBO::getCid, Function.identity(), (k1, k2) -> k1));
            Map<Integer, SmallCategoryInventoryBO> inventoryMap = inventoryList.stream()
                    .collect(Collectors.toMap(SmallCategoryInventoryBO::getCid, Function.identity(), (k1, k2) -> k1));
            Map<Integer, SmallCategoryInventoryBO> inventoryOnMap = inventoryOnList.stream()
                    .collect(Collectors.toMap(SmallCategoryInventoryBO::getCid, Function.identity(), (k1, k2) -> k1));
            Map<Integer, DisplayProductInfoBO> displayMap = displayProductInfoList.stream()
                    .collect(Collectors.toMap(DisplayProductInfoBO::getCid, Function.identity(), (k1, k2) -> k1));
            return wrapperRes1(resMap, inventoryMap, inventoryOnMap, req, displayMap);
        }
    }

    @Override
    public Boolean batchPush(SmallBatchPushReq req, OaUserBO staffId) {
        String message = req.getMessage();
        String extendMsg = String.format("请到oa“退换小件旧件列表” 查询“接件人为自己”的“处理中”小件旧件进行发回总部。（消息推送操作人：%s）", staffId.getUserName());
        List<Integer> smallProIds = req.getSmallProIds();
        List<BatchPushBO> bos = baseMapper.getBatchPushCh999Ids(smallProIds);
        //没有接件人的小件单
        List<Integer> noch999IdSmallIds = bos.stream().filter(e -> ObjectUtil.isNull(e.getCh999Id())).map(BatchPushBO::getSmallProId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(noch999IdSmallIds)) {
            throw new CustomizeException(StrUtil.format("小件单[{}]为系统接件,没法批量推送通知", noch999IdSmallIds.stream()
                    .map(Convert::toStr).collect(Collectors.joining(StringPool.COMMA))));
        }

        String ch999Ids = bos.stream().map(e -> Convert.toStr(e.getCh999Id())).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
        String url = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST) + String.format(SmallProRelativePathConstant.NOTICE_WEIXIN, ch999Ids, message + extendMsg);
//        String url = inwcfUrlSource.getNoticeWeixin(ch999Ids, message);
        HttpClientUtil.get(url, new HashMap<>(0));
        // 推送后再记录日志
        for (BatchPushBO bo : bos) {
            Integer ch999Id = bo.getCh999Id();
            String ch999Name = bo.getCh999Name();
            Integer smallProId = bo.getSmallProId();
            smallproLogService.addLogs(smallProId, message + "( 微信(APP) 已对接通知：" + ch999Name + ")",
                    staffId.getUserName(), 0);
        }
        return true;
    }

    @Override
    public R<SmallProYuyueInfo> getYuyueInfoByMobile(String mobile,Integer yuYueId) {
        SmallProYuyueInfo res = new SmallProYuyueInfo();
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Integer yyId = baseMapper.getYuyueIdByMobile(oaUserBO.getAreaId(), mobile);
        if (CommenUtil.isNotNullZero(yyId) && yyId.equals(yuYueId)) {
            res.setId(yyId);
            res.setNoticeMsg("客户已预约到店，预约单号为：" + yyId + "，请注意跟进处理，避免重复录单。");
        }

        return R.success(res);
    }

    @Override
    public R<Boolean> testWithoutProblem(Integer checkStatus, Integer fcId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        //权限校验 后台地区在d1 后台，并且权限和报废按钮权限一致，才允许操作
        AreaBelongsDcHqD1AreaId areaBelongsDcHqD1AreaId = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
        if (areaBelongsDcHqD1AreaId == null || !Objects.equals(areaBelongsDcHqD1AreaId.getD1AreaId(), oaUserBO.getAreaId())) {
            return R.error(StrUtil.format("当前登录地区非{}仓库", areainfoService.lambdaQuery()
                    .eq(Areainfo::getId, areaBelongsDcHqD1AreaId.getD1AreaId()).select(Areainfo::getArea).list()
                    .stream().map(Areainfo::getArea).findFirst().orElse("D1")));
        }
        if (!oaUserBO.getRank().contains(smallProConstant.BAO_FEI_RANKS) && !oaUserBO.getUserId().equals(13272)) {
            return R.error("您没有权限！权值：6f1");
        }
        boolean update = shouhouFanchangService.update(new LambdaUpdateWrapper<ShouhouFanchang>()
                .set(ShouhouFanchang::getCheckStatus, checkStatus)
                .set(ShouhouFanchang::getCheckInUser, oaUserBO.getUserName())
                .set(ShouhouFanchang::getCheckTime, LocalDateTime.now())
                .eq(ShouhouFanchang::getId, fcId));

        return update ? R.success("操作成功") : R.error("操作失败！");
    }

    @Override
    public R<List<FilmAccessoriesBo>> getFilmAccessories(Integer ppid, HttpServletRequest request) {
        String token = request.getHeader("authorization");
        R<String> hostR = sysConfigClient.getValueByCode(SysConfigConstant.M_URL);
        if (hostR.getCode() != ResultCode.SUCCESS || hostR.getData() == null) {
            return R.error("获取配置项失败");
        }
        String host = hostR.getData();
        String url = host + String.format(SmallProRelativePathConstant.FILM_ACCESSORIES, ppid);
        String jsonStr = HttpRequest.get(url).header("authorization", token).execute().body();
        List<FilmAccessoriesBo> result = new LinkedList<>();
        if (StringUtils.isNotEmpty(jsonStr)) {
            R<List<FilmAccessoriesBo>> r = JSONObject.parseObject(jsonStr, R.class);
            List<FilmAccessoriesBo> data = r.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                return R.success(data);
            }
        }
        List<Productinfo> list = productinfoService.list(new LambdaQueryWrapper<Productinfo>().eq(Productinfo::getPpriceid, ppid));
        if (CollectionUtils.isNotEmpty(list)) {
            Productinfo productinfo = list.get(0);
            FilmAccessoriesBo item = new FilmAccessoriesBo();
            item.setBarCode(productinfo.getBarCode());
            item.setPid(productinfo.getProductId());
            item.setPpid(productinfo.getPpriceid());
            item.setSkuName(productinfo.getProductName());
            item.setPrice(productinfo.getMemberprice());
            result = Arrays.asList(item);
        }
        return R.success(result);
    }

    @Override
    public R<List<FilmAccessoriesV2Item>> getFilmAccessoriesV2(Integer ppid, Integer type, Integer basketId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        R<String> hostR = sysConfigClient.getValueByCode(SysConfigConstant.M_URL);
        if (hostR.getCode() != ResultCode.SUCCESS || hostR.getData() == null) {
            return R.error("获取配置项失败");
        }
        List<FilmAccessoriesV2Item> result = new LinkedList<>();
        List<FilmAccessoriesV2Item> tempList = new LinkedList<>();
        FilmAccessoriesV2Data data = new FilmAccessoriesV2Data();
        SmallProServiceTypeEnum serviceTypeEnum = EnumUtil.getEnumByCode(SmallProServiceTypeEnum.class, type);
        Integer webType = NumberConstant.TWO;
        if (serviceTypeEnum != null) {
            webType = serviceTypeEnum.getWebCode();
        }
        Result<List<FilmAccessoriesV2Data>> r = imCloud.getFilmAccessoriesV2(ppid, webType);
        if (r != null && CollectionUtils.isNotEmpty(r.getData())) {
            data = r.getData().get(0);
            tempList = data.getProductDataList();
        }
        List<Productinfo> list = productinfoService.list(new LambdaQueryWrapper<Productinfo>()
                .select(Productinfo::getPpriceid, Productinfo::getProductName, Productinfo::getMemberprice, Productinfo::getBarCode)
                .eq(Productinfo::getPpriceid, ppid));
        if (CollectionUtils.isEmpty(list)) {
            return R.error("获取当前商品价格出错");
        }
        if (CollectionUtils.isEmpty(tempList)) {
            //如果网站接口返回的数据为空，则取同一ppid的商品信息
            FilmAccessoriesV2Item item = new FilmAccessoriesV2Item();
            if (CollectionUtils.isNotEmpty(list)) {
                Productinfo productinfo = list.get(0);
                item.setPpid(productinfo.getPpriceid());
                item.setProductName(productinfo.getProductName());
                item.setProductColor(productinfo.getProductColor());
                item.setDifference(BigDecimal.ZERO);
                Integer leftCount = baseMapper.getProductKcLeftCountByPpidAndAreaId(item.getPpid(), oaUserBO.getAreaId());
                item.setLeftCount(leftCount);
                item.setBarCode(productinfo.getBarCode());
            }
            result = Arrays.asList(item);
            return R.success(result);
        }
        Boolean isMakeUpPrice = data.getIsMakeUpPrice();
        BigDecimal currentProductPrice = list.get(0).getMemberprice();
        BigDecimal makeUpPricePercent = data.getMakeUpPricePercent() == null ? BigDecimal.ZERO : data.getMakeUpPricePercent();
        Integer makeUpPriceWay = data.getMakeUpPriceWay();

        List<Integer> ppidList = tempList.stream().map(FilmAccessoriesV2Item::getPpid).collect(Collectors.toList());
        List<ProductKc> productKcList = listProductKc(oaUserBO, ppidList);
        List<Productinfo> productInfoList = productinfoService.list(new LambdaQueryWrapper<Productinfo>()
                .select(Productinfo::getPpriceid, Productinfo::getProductName, Productinfo::getMemberprice, Productinfo::getBarCode)
                .eq(Productinfo::getPpriceid, ppid));
        //网站数据处理
        result = tempList.stream().map(item -> {
            if (isMakeUpPrice && (currentProductPrice.compareTo(item.getMemberPrice()) < 0 || Objects.equals(item.getMakeUpPriceWay(), 2))) {
                //如果需要补差价、且置换商品价格高于当前商品价格，计算差价金额
                if (Objects.equals(item.getMakeUpPriceWay(), 1)) {
                    //按差价金额补
                    BigDecimal difference = item.getMemberPrice().subtract(currentProductPrice).multiply(makeUpPricePercent).divide(BigDecimal.valueOf(100L), 2, BigDecimal.ROUND_HALF_UP);
                    item.setDifference(difference);
                } else {
                    //按换货商品金额补
                    BigDecimal difference = item.getMemberPrice().multiply(makeUpPricePercent).divide(BigDecimal.valueOf(100L), 2, BigDecimal.ROUND_HALF_UP);
                    item.setDifference(difference);
                }
            } else {
                item.setDifference(BigDecimal.ZERO);
            }
            item.setIsMakeUpPrice(isMakeUpPrice);
            item.setMakeUpPricePercent(makeUpPricePercent);
            BigDecimal subtractPrice = item.getMemberPrice().subtract(currentProductPrice);
            item.setSubtractPrice(subtractPrice.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtractPrice);
            if (isMakeUpPrice && CommenUtil.isNotNullZero(makeUpPriceWay)) {
                item.setMakeUpPriceWay(makeUpPriceWay);
                item.setMakeUpPriceWayText(EnumUtil.getMessageByCode(MakeUpPriceWayEnum.class, makeUpPriceWay));
            } else {
                item.setMakeUpPriceWayText("无");
            }
            ProductKc productKc = productKcList.stream().filter(e -> Objects.equals(item.getPpid(), e.getPpriceid())).findFirst().orElse(null);
            item.setLeftCount(Optional.ofNullable(productKc).map(ProductKc::getLeftCount).orElse(NumberConstant.ZERO));
            Productinfo productInfo = productInfoList.stream().filter(p -> Objects.equals(item.getPpid(), p.getPpriceid())).findFirst().orElse(null);
            item.setBarCode(Optional.ofNullable(productInfo).map(Productinfo::getBarCode).orElse(""));
            return item;
        }).sorted(Comparator.comparing(FilmAccessoriesV2Item::getLeftCount, Comparator.reverseOrder())).collect(Collectors.toList());
        //判断是否是壳年包 根据ppid判断类型
        List<Integer> cidList = productinfoService.getCidsByPpid(Collections.singletonList(ppid));
        if (CollUtil.isNotEmpty(CollUtil.intersection(cidList, CollUtil.toList(43, 218, 217, 464)))
                && Objects.equals(type, SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode())) {
            if (CommenUtil.isNullOrZero(basketId)) {
                return R.success(result);
            }
            Basket basket = getFilmCardBasket(basketId);
            BigDecimal canRefundPrice = NumberUtil.toBigDecimal(basket.getPrice());
            for (FilmAccessoriesV2Item filmAccessoriesV2Item : result) {
                //置换商品网站的售价
                BigDecimal memberPrice = Optional.ofNullable(filmAccessoriesV2Item.getMemberPrice()).orElse(BigDecimal.ZERO);
                //商品价格-服务价格（显示正数即可，负数显示为0）
                BigDecimal subtract = DecideUtil.iif(memberPrice.subtract(canRefundPrice).compareTo(BigDecimal.ZERO) > 0, memberPrice.subtract(canRefundPrice), BigDecimal.ZERO);
                //系数
                BigDecimal makeUpPricePercent1 = filmAccessoriesV2Item.getMakeUpPricePercent();
                BigDecimal mul = NumberUtil.mul(NumberUtil.div(makeUpPricePercent1, 100), subtract);
                filmAccessoriesV2Item.setDifference(mul);
                //添加显示
                filmAccessoriesV2Item.setMassage(String.format("服务价格 %s，商品价格 %s，需补差价 %s", canRefundPrice.setScale(SCALE, RoundingMode.HALF_UP), memberPrice.setScale(SCALE, RoundingMode.HALF_UP), mul.setScale(SCALE, RoundingMode.HALF_UP)));
            }
            return R.success(result);
        }
        return R.success(result);
    }

    private Basket getFilmCardBasket(Integer basketId) {
        return SpringContextUtil.reqCache(()-> {
            //获取服务购买服务时的价格 查询服务价格
            FilmCardInfomationBO filmCardInfoByBasketId = smallproFilmCardService.getFilmCardInfoByBasketId(basketId);
            if(filmCardInfoByBasketId == null){
                return null;
            }
            Basket basket = Optional.ofNullable(basketService.getByIdSqlServer(filmCardInfoByBasketId.getBasketId())).orElseGet(() -> {
                //查询不到从历史库查询
                AtomicReference<Basket> basketRef = new AtomicReference<>();
                new MultipleTransaction().execute(DataSourceConstants.OA_NEW_HIS, () ->
                                basketRef.set(basketService.getByIdSqlServer(filmCardInfoByBasketId.getBasketId())))
                        .commit();
                return basketRef.get();
            });
            return basket;
        }, RequestCacheKeys.SMALLPRO_SERVICE_GET_FILM_CARD_BASKET, basketId);
    }

    private List<ProductKc> listProductKc(OaUserBO oaUserBO, List<Integer> ppidList) {
        if (CollUtil.isEmpty(ppidList)) {
            return Collections.emptyList();
        }
        return productKcService.list(new LambdaQueryWrapper<ProductKc>().select(ProductKc::getPpriceid, ProductKc::getLeftCount, ProductKc::getInprice)
                .in(ProductKc::getPpriceid, ppidList).eq(ProductKc::getAreaid, oaUserBO.getAreaId()));
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<List<SmallProOldGoodsWaitingForSelectRes>> getSmallProOldGoodsWaitingForSelect(String area) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (StringUtils.isEmpty(area)) {
            area = oaUserBO.getArea();
        }
        Integer areaId = oaUserBO.getAreaId();
        R<AreaInfoBasicVO> areaInfoR = areaInfoClient.getByArea(area);
        if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
            areaId = areaInfoR.getData().getId();
        }
        List<SmallProOldGoodsWaitingForSelectRes> resList = baseMapper.getSmallProOldGoodsWaitingForSelect(areaId);
        if (CollectionUtils.isNotEmpty(resList)) {
            resList.removeIf(item -> CommenUtil.isNullOrZero(item.getNumber()));
        }
        return R.success(resList);
    }

    private void limitTime(SmallCategoryReq req) {
        // 限制查最近三个月的
        LocalDateTime startTime = req.getStartTime();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime limitTime = now.plusDays(-90);
        if (null == startTime || startTime.isBefore(limitTime)) {
            req.setStartTime(limitTime);
            req.setEndTime(now);
        }
    }

    private CommonDataRes wrapperRes1(Map<Integer, SmallCategoryBO> resMap, Map<Integer,
            SmallCategoryInventoryBO> inventoryMap, Map<Integer, SmallCategoryInventoryBO> inventoryOnMap,
                                      SmallCategoryReq req, Map<Integer, DisplayProductInfoBO> displayMap) {
        CommonDataRes commonDataRes = new CommonDataRes();
        setColumn(req, commonDataRes);
        LocalDateTime startTime = req.getStartTime();
        LocalDateTime endTime = req.getEndTime();
        long differDay = 1L;
        if (null != startTime && null != endTime) {
            Duration between = Duration.between(startTime, endTime);
            differDay = between.toDays();
            differDay += 1;
        }
        Set<Integer> ciDs = new HashSet<>();
        ciDs.addAll(resMap.keySet());
        ciDs.addAll(inventoryMap.keySet());
        ciDs.addAll(inventoryOnMap.keySet());
        if (CollectionUtils.isEmpty(ciDs)) {
            return commonDataRes;
        }
        List<SmallCategoryRes> res = new ArrayList<>();
        Long finalDifferDay = differDay;
        List<SmallCategoryRes> finalRes = res;
        ciDs.forEach((Integer e) -> {
            if (null == e) {
                return;
            }
            SmallCategoryRes categoryRes = new SmallCategoryRes();
            setSmallCategoryRes(resMap, inventoryMap, inventoryOnMap, displayMap, finalDifferDay, e, categoryRes);
            finalRes.add(categoryRes);
        });
        BigDecimal totalProfit = res.stream().map(SmallCategoryRes::getProfit).reduce(BigDecimal.ZERO, BigDecimal::add);
        res = res.stream().sorted(Comparator.comparing(SmallCategoryRes::getProfit).reversed()).collect(Collectors.toList());
        SmallCategoryRes totalBean = getSmallCategoryResTotal(res, finalDifferDay, totalProfit);
        commonDataRes.setList(res);
        commonDataRes.setTotal(totalBean);
        return commonDataRes;
    }

    private static SmallCategoryRes getSmallCategoryResTotal(List<SmallCategoryRes> res, Long finalDifferDay, BigDecimal totalProfit) {
        SmallCategoryRes totalBean = new SmallCategoryRes();
        Integer saleCountTotal = 0;
        Integer inventoryCountTotal = 0;
        BigDecimal saleAmountTotal = BigDecimal.ZERO;
        BigDecimal inventoryCostTotal = BigDecimal.ZERO;
        BigDecimal profitTotal = BigDecimal.ZERO;
        for (int i = 0; i < res.size(); i++) {
            SmallCategoryRes categoryRes = res.get(i);
            categoryRes.setOrders("第" + (i + 1) + "名");
            if (null != totalProfit && BigDecimal.ZERO.compareTo(totalProfit) != 0) {
                categoryRes.setProfitRatio(categoryRes.getProfit().divide(totalProfit, PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP) + "%");
            }
            saleCountTotal += categoryRes.getSaleCount();
            inventoryCountTotal += categoryRes.getInventoryCount();
            saleAmountTotal = saleAmountTotal.add(categoryRes.getSaleAmount());
            inventoryCostTotal = inventoryCostTotal.add(categoryRes.getInventoryCost());
            profitTotal = profitTotal.add(categoryRes.getProfit());
        }
        totalBean.setSaleCount(saleCountTotal);
        totalBean.setInventoryCount(inventoryCountTotal);
        if (saleCountTotal == 0) {
            totalBean.setStockToSalesRatio(BigDecimal.ZERO);
        } else {
            totalBean.setStockToSalesRatio(new BigDecimal(inventoryCountTotal)
                    .divide(new BigDecimal(saleCountTotal), SCALE, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(finalDifferDay)));
        }
        totalBean.setStockToSalesRatio(BigDecimal.valueOf(totalBean.getStockToSalesRatio().longValue()));
        totalBean.setSaleAmount(saleAmountTotal);
        totalBean.setInventoryCost(inventoryCostTotal);
        totalBean.setProfit(profitTotal);
        totalBean.setProfitRatio("1");
        return totalBean;
    }

    private static SmallCategoryRes getSmallCategoryResTotal(List<SmallCategoryRes> res, SmallCategoryRes totalBean) {
        Long saleCountTotal = 0L;
        Long preSaleCountTotal = 0L;
        BigDecimal profitTotal = BigDecimal.ZERO;
        BigDecimal preProfitTotal = BigDecimal.ZERO;
        BigDecimal preSaleAmountTotal = BigDecimal.ZERO;
        for (SmallCategoryRes categoryRes : res) {
            saleCountTotal += categoryRes.getSaleCount();
            preSaleCountTotal += categoryRes.getPreSaleCount();
            profitTotal = profitTotal.add(categoryRes.getProfit());
            preProfitTotal = preProfitTotal.add(categoryRes.getPreProfit());
            preSaleAmountTotal = preSaleAmountTotal.add(categoryRes.getPreSaleAmount());
        }
        if (preSaleCountTotal == 0) {
            totalBean.setSaleCountRatioChain(BigDecimal.ZERO + "%");
        } else {
            totalBean.setSaleCountRatioChain(BigDecimal.valueOf(saleCountTotal - preSaleCountTotal)
                    .divide(BigDecimal.valueOf(preSaleCountTotal), PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP) + "%");
            totalBean.setPreSaleCount(preSaleCountTotal.intValue());
            totalBean.setPreSaleAmount(preSaleAmountTotal.setScale(SCALE, RoundingMode.HALF_UP));
        }
        if (BigDecimal.ZERO.compareTo(preProfitTotal) == 0) {
            totalBean.setProfitRatioChain(BigDecimal.ZERO + "%");
        } else {
            totalBean.setProfitRatioChain(profitTotal.subtract(preProfitTotal)
                    .divide(preProfitTotal, PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP) + "%");
            totalBean.setPreProfit(preProfitTotal);
        }
        return totalBean;
    }

    private static void setSmallCategoryRes(Map<Integer, SmallCategoryBO> resMap,
                                            Map<Integer, SmallCategoryInventoryBO> inventoryMap,
                                            Map<Integer, SmallCategoryInventoryBO> inventoryOnMap,
                                            Map<Integer, DisplayProductInfoBO> displayMap,
                                            Long finalDifferDay, Integer e, SmallCategoryRes categoryRes) {
        String name = Optional.ofNullable(inventoryMap.get(e)).map(SmallCategoryInventoryBO::getCategoryName).orElseGet(() ->
                Optional.ofNullable(inventoryOnMap.get(e)).map(SmallCategoryInventoryBO::getCategoryName).orElseGet(() ->
                        Optional.ofNullable(resMap.get(e)).map(SmallCategoryBO::getCategoryName).orElseGet(() -> "")
                )
        );
        categoryRes.setCategoryName(name);
        categoryRes.setCategoryId(e);
        int v = 0;
        categoryRes.setSaleCount(Optional.ofNullable(resMap.get(e)).map(SmallCategoryBO::getSaleCount).orElseGet(() -> v));
        categoryRes.setInventoryCount(Optional.ofNullable(inventoryMap.get(e)).map(SmallCategoryInventoryBO::getLCount).orElseGet(() -> v) +
                Optional.ofNullable(inventoryOnMap.get(e)).map(SmallCategoryInventoryBO::getLCount).orElseGet(() -> v) -
                Optional.ofNullable(displayMap.get(e)).map(DisplayProductInfoBO::getClCount).orElseGet(() -> v));
        Integer saleCount = categoryRes.getSaleCount();
        if (null == finalDifferDay || finalDifferDay.compareTo(0L) == 0 || null == saleCount || saleCount == 0) {
            categoryRes.setStockToSalesRatio(BigDecimal.ZERO);
        } else {
            categoryRes.setStockToSalesRatio(new BigDecimal(categoryRes.getInventoryCount())
                    .divide(new BigDecimal(saleCount), SCALE, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(finalDifferDay)));
            //取整
            categoryRes.setStockToSalesRatio(BigDecimal.valueOf(categoryRes.getStockToSalesRatio().longValue()));
        }
        categoryRes.setSaleAmount(Optional.ofNullable(resMap.get(e)).map(SmallCategoryBO::getSaleAmount).orElseGet(() -> BigDecimal.ZERO));
        categoryRes.setInventoryCost(Optional.ofNullable(inventoryMap.get(e)).map(SmallCategoryInventoryBO::getCost).orElseGet(() -> BigDecimal.ZERO).add(
                Optional.ofNullable(inventoryOnMap.get(e)).map(SmallCategoryInventoryBO::getCost).orElseGet(() -> BigDecimal.ZERO))
                .subtract(Optional.ofNullable(displayMap.get(e)).map(DisplayProductInfoBO::getClCost).orElseGet(() -> BigDecimal.ZERO)).setScale(SCALE, RoundingMode.HALF_UP));
        categoryRes.setProfit(Optional.ofNullable(resMap.get(e)).map(SmallCategoryBO::getProfitAmount).orElseGet(() -> BigDecimal.ZERO));
    }

    private CommonDataRes wrapperRes4(Map<Integer, List<SmallCategoryBO>> resMap, Map<Integer,
            List<SmallCategoryInventoryBO>> inventoryMap, Map<Integer, List<SmallCategoryInventoryBO>> inventoryOnMap,
                                      SmallCategoryReq req, Map<Integer, String> categoryNameMap, Map<Integer, List<DisplayProductInfoBO>> displayMap) {
        CommonDataRes commonDataRes = new CommonDataRes();
        setColumn(req, commonDataRes);
        LocalDateTime startTime = req.getStartTime();
        LocalDateTime endTime = req.getEndTime();
        long differDay = 1L;

        if (null != startTime && null != endTime) {
            Duration between = Duration.between(startTime, endTime);
            differDay = between.toDays();
            differDay += 1;
        }

        Set<Integer> ciDs = new HashSet<>();
        ciDs.addAll(resMap.keySet());
        ciDs.addAll(inventoryMap.keySet());
        ciDs.addAll(inventoryOnMap.keySet());
        if (CollectionUtils.isEmpty(ciDs)) {
            return commonDataRes;
        }
        Long finalDifferDay = differDay;
        List<SmallCategoryRes> res = new ArrayList<>();
        List<SmallCategoryRes> finalRes = res;
        ciDs.forEach((Integer e) -> {
            if (null == e) {
                return;
            }
            SmallCategoryRes categoryRes = new SmallCategoryRes();
            categoryRes.setCategoryId(e);
            categoryRes.setCategoryName(categoryNameMap.get(e));
            categoryResData(resMap, inventoryMap, inventoryOnMap, displayMap, finalDifferDay, e, categoryRes);

            finalRes.add(categoryRes);
        });
        BigDecimal totalProfit = res.stream().map(SmallCategoryRes::getProfit).reduce(BigDecimal.ZERO, BigDecimal::add);
        res = res.stream().sorted(Comparator.comparing(SmallCategoryRes::getProfit).reversed()).collect(Collectors.toList());
        SmallCategoryRes totalBean = getSmallCategoryResTotal(res, finalDifferDay, totalProfit);
        commonDataRes.setList(res);
        commonDataRes.setTotal(totalBean);
        return commonDataRes;
    }

    private static void categoryResData(Map<Integer, List<SmallCategoryBO>> resMap,
                                        Map<Integer, List<SmallCategoryInventoryBO>> inventoryMap,
                                        Map<Integer, List<SmallCategoryInventoryBO>> inventoryOnMap,
                                        Map<Integer, List<DisplayProductInfoBO>> displayMap,
                                        Long finalDifferDay, Integer e, SmallCategoryRes categoryRes) {
        int v = 0;
        if (CollectionUtils.isNotEmpty(resMap.get(e))) {
            categoryRes.setSaleCount(resMap.get(e).stream().mapToInt(SmallCategoryBO::getSaleCount).sum());
            categoryRes.setSaleAmount(BigDecimal.valueOf(
                    resMap.get(e).stream().mapToDouble(x -> x.getSaleAmount().doubleValue())
                            .sum()).setScale(SCALE, RoundingMode.HALF_UP));
            categoryRes.setProfit(BigDecimal.valueOf(
                    resMap.get(e).stream().mapToDouble(x -> x.getProfitAmount().doubleValue())
                            .sum()).setScale(SCALE, RoundingMode.HALF_UP));
        } else {
            categoryRes.setSaleCount(v);
            categoryRes.setSaleAmount(BigDecimal.ZERO);
            categoryRes.setProfit(BigDecimal.ZERO);
        }

        if (CollectionUtils.isNotEmpty(inventoryMap.get(e))) {
            categoryRes.setInventoryCount(inventoryMap.get(e).stream().mapToInt(SmallCategoryInventoryBO::getLCount).sum());
            categoryRes.setInventoryCost(BigDecimal.valueOf(
                    inventoryMap.get(e).stream().mapToDouble(x -> x.getCost().doubleValue())
                            .sum()).setScale(SCALE, RoundingMode.HALF_UP));
        } else {
            categoryRes.setInventoryCount(v);
            categoryRes.setInventoryCost(BigDecimal.ZERO);
        }

        if (CollectionUtils.isNotEmpty(inventoryOnMap.get(e))) {
            categoryRes.setInventoryCount(categoryRes.getInventoryCount() + inventoryOnMap.get(e).stream().mapToInt(SmallCategoryInventoryBO::getLCount).sum());
            double cost = inventoryOnMap.get(e).stream().mapToDouble(x -> x.getCost().doubleValue()).sum();
            categoryRes.setInventoryCost(categoryRes.getInventoryCost().add(BigDecimal.valueOf(cost).setScale(SCALE, RoundingMode.HALF_UP)));
        }

        if (CollectionUtils.isNotEmpty(displayMap.get(e))) {
            int clCount = displayMap.get(e).stream().mapToInt(DisplayProductInfoBO::getClCount).sum();
            double cost = displayMap.get(e).stream().mapToDouble(x -> x.getClCost().doubleValue()).sum();
            categoryRes.setInventoryCost(categoryRes.getInventoryCost().subtract(BigDecimal.valueOf(cost)).setScale(SCALE, RoundingMode.HALF_UP));
            categoryRes.setInventoryCount(categoryRes.getInventoryCount() - clCount);
        }

        Integer saleCount = categoryRes.getSaleCount();
        long v1 = 0L;
        if (finalDifferDay.compareTo(v1) == 0 || null == saleCount || saleCount == v) {
            categoryRes.setStockToSalesRatio(BigDecimal.ZERO);
        } else {
            categoryRes.setStockToSalesRatio(new BigDecimal(categoryRes.getInventoryCount())
                    .divide(new BigDecimal(saleCount), SCALE, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(finalDifferDay)));
            //取整
            categoryRes.setStockToSalesRatio(BigDecimal.valueOf(categoryRes.getStockToSalesRatio().longValue()));
        }
    }

    private static CommonDataRes wrapperRes3(CommonDataRes commonDataRes, Map<Integer, List<SmallCategoryBO>> resMap, int wscale) {
        Set<Integer> ciDs = new HashSet<>();
        ciDs.addAll(resMap.keySet());
        if (CollectionUtils.isEmpty(ciDs)) {
            return commonDataRes;
        }

        int v = 0;
        List<SmallCategoryRes> list = commonDataRes.getList();
        for (SmallCategoryRes res : list) {
            if (CollectionUtils.isNotEmpty(resMap.get(res.getCategoryId()))) {
                res.setPreSaleCount(resMap.get(res.getCategoryId()).stream().mapToInt(SmallCategoryBO::getSaleCount).sum());
                res.setPreSaleAmount(BigDecimal.valueOf(
                        resMap.get(res.getCategoryId()).stream().mapToDouble(x -> x.getSaleAmount().doubleValue())
                                .sum()).setScale(wscale, RoundingMode.HALF_UP));
                res.setPreProfit(BigDecimal.valueOf(
                        resMap.get(res.getCategoryId()).stream().mapToDouble(x -> x.getProfitAmount().doubleValue())
                                .sum()).setScale(wscale, RoundingMode.HALF_UP));

                getRatioChain(v, res);
            } else {
                res.setPreSaleCount(v);
                res.setPreSaleAmount(BigDecimal.ZERO);
                res.setPreProfit(BigDecimal.ZERO);
                res.setSaleCountRatioChain(v + "%");
                res.setSaleAmountRatioChain(v + "%");
                res.setProfitRatioChain(v + "%");
            }
        }

        //计算合计环比
        SmallCategoryRes totalBean = (SmallCategoryRes) commonDataRes.getTotal();
        commonDataRes.setTotal(getSmallCategoryResTotal(list, totalBean));
        return commonDataRes;
    }

    private static void getRatioChain(int v, SmallCategoryRes res) {
        if (res.getPreSaleCount() == v) {
            res.setSaleCountRatioChain(v + "%");
        } else {
            BigDecimal saleCountRatioChain;
            saleCountRatioChain = BigDecimal.valueOf(res.getSaleCount()).subtract(BigDecimal.valueOf(res.getPreSaleCount()));
            saleCountRatioChain = saleCountRatioChain.divide(BigDecimal.valueOf(res.getPreSaleCount()), PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP);
            res.setSaleCountRatioChain(saleCountRatioChain + "%");
        }

        if (BigDecimal.ZERO.compareTo(res.getPreSaleAmount()) == 0) {
            res.setSaleAmountRatioChain(v + "%");
        } else {
            BigDecimal saleAmountRatioChain = res.getSaleAmount().subtract(res.getPreSaleAmount());
            saleAmountRatioChain = saleAmountRatioChain.divide(res.getPreSaleAmount(), PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP);
            res.setSaleAmountRatioChain(saleAmountRatioChain + "%");
        }

        if (BigDecimal.ZERO.compareTo(res.getPreProfit()) == 0) {
            res.setProfitRatioChain(v + "%");
        } else {
            BigDecimal profitRatioChain = res.getProfit().subtract(res.getPreProfit());
            profitRatioChain = profitRatioChain.divide(res.getPreProfit(), PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP);
            res.setProfitRatioChain(profitRatioChain + "%");
        }
    }

    private static CommonDataRes wrapperRes2(CommonDataRes commonDataRes, Map<Integer, SmallCategoryBO> resMap) {
        Set<Integer> ciDs = new HashSet<>(resMap.keySet());
        if (CollectionUtils.isEmpty(ciDs)) {
            return commonDataRes;
        }
        List<SmallCategoryRes> list = commonDataRes.getList();
        int v = 0;
        for (SmallCategoryRes res : list) {
            res.setPreSaleCount(Optional.ofNullable(resMap.get(res.getCategoryId())).map(SmallCategoryBO::getSaleCount).orElseGet(() -> v));
            res.setPreSaleAmount(Optional.ofNullable(resMap.get(res.getCategoryId())).map(SmallCategoryBO::getSaleAmount).orElseGet(() -> BigDecimal.ZERO));
            res.setPreProfit(Optional.ofNullable(resMap.get(res.getCategoryId())).map(SmallCategoryBO::getProfitAmount).orElseGet(() -> BigDecimal.ZERO));

            if (res.getPreSaleCount() == v) {
                res.setSaleCountRatioChain(v + "%");
            } else {
                BigDecimal saleCountRatioChain;
                saleCountRatioChain = BigDecimal.valueOf(res.getSaleCount()).subtract(BigDecimal.valueOf(res.getPreSaleCount()));
                saleCountRatioChain = saleCountRatioChain.divide(BigDecimal.valueOf(res.getPreSaleCount()), PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP);
                res.setSaleCountRatioChain(saleCountRatioChain + "%");
            }

            if (BigDecimal.ZERO.compareTo(res.getPreSaleAmount()) == 0) {
                res.setSaleAmountRatioChain(v + "%");
            } else {
                BigDecimal saleAmountRatioChain = res.getSaleAmount().subtract(res.getPreSaleAmount());
                saleAmountRatioChain = saleAmountRatioChain.divide(res.getPreSaleAmount(), PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP);
                res.setSaleAmountRatioChain(saleAmountRatioChain + "%");
            }

            if (BigDecimal.ZERO.compareTo(res.getPreProfit()) == 0) {
                res.setProfitRatioChain(v + "%");
            } else {
                BigDecimal profitRatioChain = res.getProfit().subtract(res.getPreProfit());
                profitRatioChain = profitRatioChain.divide(res.getPreProfit(), PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(PERCENTAGE)).setScale(SCALE, RoundingMode.HALF_UP);
                res.setProfitRatioChain(profitRatioChain + "%");
            }
        }

        SmallCategoryRes totalBean = (SmallCategoryRes) commonDataRes.getTotal();
        commonDataRes.setTotal(getSmallCategoryResTotal(list, totalBean));
        return commonDataRes;
    }

    private void setColumn(SmallCategoryReq req, CommonDataRes commonDataRes) {
        List<String> statistics = req.getStatistics();
        List<CommonDataRes.ColumnsBean> collect = statistics.stream().map((String e) -> {
            CommonDataRes.ColumnsBean columnsBean = new CommonDataRes.ColumnsBean();
            columnsBean.setTitle(EnumUtil.getMessageByCode(SmallCategoryStatisticsEnum.class, e));
            columnsBean.setKey(e);
            columnsBean.setWidth("100px");
            columnsBean.setLink(false);
            columnsBean.setCopyParm(false);
            return columnsBean;
        }).collect(Collectors.toList());
        commonDataRes.setColumns(collect);
    }

    private List<Integer> getAreaIds(List<Integer> areaIds, List<Integer> administrativeAreaIds) {
        //获取当前登陆人信息
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        //获取当前登陆人的门店的id以及授权
        Integer areaId = oaUserBO.getAreaId();
        Integer authorizeId = oaUserBO.getAuthorizeId();
        List<Authorize> authorizeList = authorizeService.lambdaQuery().eq(Authorize::getId, authorizeId).list();
        List<Authorize> hqCollect = authorizeList.stream().filter((Authorize item) -> {
            String hqAreaId = Optional.ofNullable(item.getHqAreaId()).orElse("");
            List<String> areaList = Arrays.asList(hqAreaId.split(","));
            return org.apache.commons.collections4.CollectionUtils.isNotEmpty(areaList) && areaList.contains(areaId.toString());
        }).collect(Collectors.toList());
        List<Authorize> dcCollect = authorizeList.stream().filter((Authorize item) -> {
            String dcAreaId = Optional.ofNullable(item.getDcAreaId()).orElse("");
            List<String> areaList = Arrays.asList(dcAreaId.split(","));
            return org.apache.commons.collections4.CollectionUtils.isNotEmpty(areaList) && areaList.contains(areaId.toString());
        }).collect(Collectors.toList());

        List<Authorize> h1Collect = authorizeList.stream().filter((Authorize item) -> {
            String h1AreaId = Optional.ofNullable(item.getH1AreaId()).orElse("");
            List<String> areaList = Arrays.asList(h1AreaId.split(","));
            return org.apache.commons.collections4.CollectionUtils.isNotEmpty(areaList) && areaList.contains(areaId.toString());
        }).collect(Collectors.toList());

        List<Authorize> d1Collect = authorizeList.stream().filter((Authorize item) -> {
            String d1AreaId = Optional.ofNullable(item.getD1AreaId()).orElse("");
            List<String> areaList = Arrays.asList(d1AreaId.split(","));
            return org.apache.commons.collections4.CollectionUtils.isNotEmpty(areaList) && areaList.contains(areaId.toString());
        }).collect(Collectors.toList());

        List<Integer> selectSmallCategoryAreaDTO = new ArrayList<>();
        //如果集合为空说明当前登陆人不是HQ以及DC的所以查自己的门店
        if (CollectionUtils.isEmpty(hqCollect) && CollectionUtils.isEmpty(dcCollect) && CollectionUtils.isEmpty(h1Collect) && CollectionUtils.isEmpty(d1Collect)) {
            selectSmallCategoryAreaDTO = authorizeMapper.querySmallCategoryArea(areaId.toString());
        } else {
            //如果hqCollect不为空就说明该登录用户是hq的人（hq查询所有的门店）
            if (CollectionUtils.isNotEmpty(hqCollect)) {
                //如果HQ的权限查询地区条件为空那就查询所有 如果不为空那就按照查询条件执行
                if (CollectionUtils.isEmpty(areaIds) && CollectionUtils.isEmpty(administrativeAreaIds)) {

                    selectSmallCategoryAreaDTO = authorizeMapper.querySmallCategoryArea(HQ_SQL);
                } else {
                    selectSmallCategoryAreaDTO.addAll(Optional.ofNullable(areaIds).orElse(new ArrayList<>()));
                    selectSmallCategoryAreaDTO.addAll(Optional.ofNullable(administrativeAreaIds).orElse(new ArrayList<>()));
                }

            }
            //如果dcCollect不为空就说明该登录用户是dc的人（dc查询的门店）
            if (CollectionUtils.isNotEmpty(dcCollect)) {
                //如果DC的权限查询地区条件为空那就查询所有 如果不为空那就按照查询条件执行
                if (CollectionUtils.isEmpty(areaIds) && CollectionUtils.isEmpty(administrativeAreaIds)) {

                    String sql = String.format(DC_SQL, areaId);
                    selectSmallCategoryAreaDTO = authorizeMapper.querySmallCategoryArea(sql);
                } else {
                    selectSmallCategoryAreaDTO.addAll(Optional.ofNullable(areaIds).orElse(new ArrayList<>()));
                    selectSmallCategoryAreaDTO.addAll(Optional.ofNullable(administrativeAreaIds).orElse(new ArrayList<>()));

                }
            }
            //如果d1Collect不为空就说明该登录用户是d1的人（d1查询的门店）
            if (CollectionUtils.isNotEmpty(d1Collect)) {
                //如果d1的权限查询地区条件为空那就查询所有 如果不为空那就按照查询条件执行
                if (CollectionUtils.isEmpty(areaIds) && CollectionUtils.isEmpty(administrativeAreaIds)) {
                    String sql = String.format(D1_SQL, areaId);
                    selectSmallCategoryAreaDTO = authorizeMapper.querySmallCategoryArea(sql);
                } else {
                    selectSmallCategoryAreaDTO.addAll(Optional.ofNullable(areaIds).orElse(new ArrayList<>()));
                    selectSmallCategoryAreaDTO.addAll(Optional.ofNullable(administrativeAreaIds).orElse(new ArrayList<>()));

                }
            }
            //如果h1Collect不为空就说明该登录用户是h1的人（h1查询的门店）
            if (CollectionUtils.isNotEmpty(h1Collect)) {
                //如果h1的权限查询地区条件为空那就查询所有 如果不为空那就按照查询条件执行
                if (CollectionUtils.isEmpty(areaIds) && CollectionUtils.isEmpty(administrativeAreaIds)) {
                    String sql = String.format(H1_SQL, areaId);
                    selectSmallCategoryAreaDTO = authorizeMapper.querySmallCategoryArea(sql);
                } else {
                    selectSmallCategoryAreaDTO.addAll(Optional.ofNullable(areaIds).orElse(new ArrayList<>()));
                    selectSmallCategoryAreaDTO.addAll(Optional.ofNullable(administrativeAreaIds).orElse(new ArrayList<>()));

                }
            }
        }
        return selectSmallCategoryAreaDTO;
    }


    /**
     * 根据basket_id获取运营商业务的状态
     *
     * @param basketIdList basketIdList
     * @return
     */
    @Override
    public List<Integer> getOperatorBasketByStatus(List<Integer> basketIdList) {
        if (basketIdList.isEmpty()) {
            return new ArrayList<>();
        }
        if(XtenantEnum.isJiujiXtenant()){
            return CommenUtil.autoQueryHist(() -> baseMapper.getOperatorBasketByStatusJiuJi(basketIdList), MTableInfoEnum.BASKET,
                    basketIdList.stream().findFirst().get());
        }
        return CommenUtil.autoQueryHist(() -> baseMapper.getOperatorBasketByStatus(basketIdList), MTableInfoEnum.BASKET,
                basketIdList.stream().findFirst().get());
    }

    /**
     * 根据basket_id获取商品是否类型（优品）
     *
     * @return
     */
    @Override
    public List<Integer> getBasketTypeById(List<Integer> basketIdList) {
        if (basketIdList.isEmpty()) {
            return new ArrayList<>();
        }
        return CommenUtil.autoQueryHist(() -> CommonUtils.bigDataInQuery(NumberConstant.TWO_THOUSAND, basketIdList,
                ids -> baseMapper.getBasketTypeById(ids)), MTableInfoEnum.BASKET, basketIdList.stream().findFirst().get());
    }

    @Override
    public R<List<FilmAccessoriesV2Item>> getFilmAccessoriesV3(Integer ppid, Integer typeParam, Integer basketId, String keyParam,
                                                               Integer mobileExchangeFlag,String imei) {
        String keyTemp = keyParam;
        if (StrUtil.isNotBlank(keyTemp) && !NumberUtil.isNumber(keyTemp)) {
            return R.error("只支持ppid或者69码查询");
        }

        if(StrUtil.length(keyTemp) >=8){
            // 69码精确查找
            Set<Integer> barCodePpids = smallProConfigService.listPpidByBarCode(Convert.toLong(keyTemp), keyTemp);
            if(barCodePpids.size() == 1){
                keyTemp = barCodePpids.stream().findFirst().map(Convert::toStr).get();
            }
        }

        String key = keyTemp;
        Integer type;
        if (NumberConstant.ONE.equals(mobileExchangeFlag)) {
            type = SmallExchangeConfigPo.ExchangeServiceTypeEnum.MOBILE_ACCESSORIES_EXCHANGE.getCode();
        }else{
            type = typeParam;
        }
        if (Objects.isNull(ppid) && NumberConstant.ONE.equals(mobileExchangeFlag)) {
            Long temp = Optional.ofNullable(basketService.getById(basketId)).map(Basket::getPpriceid).orElse(null);
            ppid = Convert.toInt(temp);
        }
        //获取配置中的信息来获取可相互换货的商品
        // 1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU 倒序排 取第一个
        Productinfo productinfo = productinfoService.getProductinfoByPpid(ppid);
        if (productinfo == null) {
            return R.success(Collections.emptyList());
        }
        // 壳膜通过网站接口获取
        if(XtenantEnum.isJiujiXtenant() && categoryService.determineShellAndFilmByPpid(ppid)
                && Stream.of(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD,
                        SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_WARRAN_TYRE_PLACEMENT)
                .anyMatch(typeEnum -> Objects.equals(type, typeEnum.getCode()))
        ){
            //壳膜年包质保换新, 走网站接口
            R<List<FilmAccessoriesV2Item>> webR = getFilmAccessoriesByWeb(productinfo, type, key, basketId, imei);

            return webR;
        }
        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(currentRequestComponent.getCurrentStaffId());
        R<List<FilmAccessoriesV2Item>> result = R.success(null);
        // key 为空 或相同的ppid 大件不能搜索同ppid
        boolean searchSamePpid = (StrUtil.isBlank(key) || StrUtil.startWith(StrUtil.toString(ppid), key))
                && !Boolean.TRUE.equals(productinfo.getIsmobile1());
        Optional<SmallExchangeConfigPo> configOpt = getConfigOpt(type, key, productinfo, searchSamePpid, basketId);
        Integer finalPpid = ppid;
        List<FilmAccessoriesV2Item> data = configOpt
                //获取配置中的分类 品牌 ppid productid
                .map(config -> {
                    result.put("policyContent", config.getPolicyContent());
                    result.addBusinessLog(StrUtil.format("匹配的配置id: {},配置类型: {}, 名称: {}", config.getId(), config.getProductConfigType(), config.getTitle()));
                    return getFilmAccessoriesV2Items(basketId, key, config, productinfo, mobileExchangeFlag,imei);
                })
                .map(faList -> setCurrentPpidFirst(finalPpid, faList, key))
                // 找不到配置同ppid可互换
                .orElseGet(() -> DecideUtil.iif(searchSamePpid,
                        () -> Collections.singletonList(getFilmAccessoriesV2Item(productinfo, oaUserOpt)),
                        () -> Collections.emptyList())
                );
        //排序处理
        sortData(data);
        result.setData(data);
        return result;
    }

    /**
     * 网站接口获取换货信息
     *
     * @param productinfo
     * @param type
     * @param key
     * @param basketId
     * @param imei
     * @return
     */
    private R<List<FilmAccessoriesV2Item>> getFilmAccessoriesByWeb(Productinfo productinfo, Integer type, String key,
                                                                   Integer basketId, String imei) {
        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(currentRequestComponent.getCurrentStaffId());
        Integer queryType = FilmAccessoriesReq.QueryTypeEnum.BY_SMALL_ITEM.getCode();
        FilmAccessoriesReq.BindMobileBasketInfoReq bindMobileBasketInfoReq = null;
        List<Integer> cidList = categoryService.tieMoCids().stream()
                //移除特殊膜
                .filter(cid -> !Arrays.asList(662).contains(cid))
                .collect(Collectors.toList());

        List<Integer> tieMoCidAll = categoryService.getProductChildCidList(cidList);
        if(StrUtil.isNotBlank(imei) && tieMoCidAll.contains(productinfo.getCid())){
            //获取机型信息
            ProductInfoListByImeiReq imeiQueryInfoBo = selectProductInfoListByImei(imei);
            if(imeiQueryInfoBo != null && imeiQueryInfoBo.getProductId() != null){
                bindMobileBasketInfoReq = FilmAccessoriesReq.BindMobileBasketInfoReq.builder().imei(imei)
                        .productId(imeiQueryInfoBo.getProductId())
                        .build();
            }
            if(bindMobileBasketInfoReq != null && bindMobileBasketInfoReq.getProductId() != null){
                queryType = FilmAccessoriesReq.QueryTypeEnum.BY_LARGE_ITEM.getCode();
            }
        }
        Optional<Basket> basketOpt = Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.getByIdSqlServer(basketId)));
        Optional<Basket> filmCardBasketOpt = Optional.ofNullable(getFilmCardBasket(basketId));

        FilmAccessoriesReq webReq = FilmAccessoriesReq.builder()
                .size(50).key(key)
                .serviceType(type).queryType(queryType)
                .srcBasketInfo(FilmAccessoriesReq.SrcBasketInfoReq.builder().ppid(productinfo.getPpriceid())
                        .basketId(basketId)
                        .filmCardBasketId(filmCardBasketOpt.map(Basket::getBasketId).orElse(null))
                        .filmCardPrice(filmCardBasketOpt.map(Basket::getPrice).orElse(null))
                        // 接件商品,当时销售价
                        .memberPrice(basketOpt.map(Basket::getPrice1).orElse(null))
                        .subCompleteDate(CommenUtil.autoQueryHist(() -> basketService.getBuyDate(basketId)))
                        .build())
                .bindBasketInfo(bindMobileBasketInfoReq)
                .build();
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "web接口请求参数: {}", webReq);
        R<List<WebFilmAccessoriesRes>> filmAccessoriesR = SpringUtil.getBean(WebCloud.class).getFilmAccessories(webReq);
        log.warn("web接口请求参数: {} , 配置id: {}, 单号: {}", webReq, Optional.ofNullable(filmAccessoriesR).map(R::getData)
                .map(List::stream).flatMap(Stream::findFirst).map(WebFilmAccessoriesRes::getConfigId).orElse(null),
                basketOpt.map(Basket::getSubId).orElse(null));
        if(!filmAccessoriesR.isSuccess()){
            log.warn("web端获取换货配置失败: {}", JSON.toJSONString(filmAccessoriesR));
            return R.error(filmAccessoriesR.getUserMsg());
        }
        List<WebFilmAccessoriesRes> accessoriesResList = filmAccessoriesR.getData();
        Optional<SmallExchangeConfigPo> configOpt = accessoriesResList.stream().findFirst()
                .map(acr -> smallProConfigService.getConfigById(acr.getConfigId()));
        // 根据配置获取购买价格
        Tuple2<BigDecimal, String> buyPriceTuple = configOpt.map(config -> getBuyPrice(basketId, config, 0))
                .orElseGet(() -> Tuples.of(BigDecimal.ZERO, ""));
        Set<Integer> ppids = new HashSet<>();
        List<FilmAccessoriesV2Item> items = accessoriesResList.stream()
                .map(SpringUtil.getBean(CommonStructMapper.class)::toFilmAccessoriesV2Item)
                .peek(fai -> configOpt.ifPresent(config -> {
                    String diffPriceTypeMessage = EnumUtil.getMessageByCode(SmallExchangeConfigPo.DifferentPriceTypeEnum.class,
                            config.getDifferentPriceType());
                    fai.setBuyPrice(buyPriceTuple.getT1());
                    fai.setIsMakeUpPrice(config.getIsDifferentPrice());
                    fai.setMakeUpPriceWay(config.getDifferentPriceType());
                    fai.setMakeUpPricePercent(config.getDifferentPricePercent());
                    fai.setMakeUpPriceWayText(diffPriceTypeMessage);
                    Tuple2<BigDecimal, String> differenceTuple = calculateDifference(config, buyPriceTuple.getT1(), fai.getMemberPrice());
                    boolean isSamePpid = Objects.equals(fai.getPpid(), productinfo.getPpriceid());
                    fai.setDifferenceText(DecideUtil.iif(isSamePpid, "同ppid忽略差价", differenceTuple.getT2()));
                    fai.setMassage(getItemMessage(buyPriceTuple.getT2(), buyPriceTuple.getT1(), fai.getMemberPrice(),
                            false, null, isSamePpid, differenceTuple.getT1()));
                    fai.setSubtractPrice(NumberUtil.null2Zero(fai.getMemberPrice()).subtract(buyPriceTuple.getT1()));
                    ppids.add(fai.getPpid());
                }))
                .collect(Collectors.toList());
        // 批量获取条形码信息
        Map<Integer, List<String>> ppidBarCodeMap = SpringUtil.getBean(ProductbarcodeService.class)
                .listBarCodeByPpids(ppids);
        // 批量获取ppid库存
        Map<Integer, Integer> leftCountMap = oaUserOpt.map(oaUser ->
                productKcService.listKcCount(ppids, oaUser.getAreaId())).orElse(Collections.emptyMap());
        items = items.stream()
                .peek(fai -> {
                    List<String> barCodeList = ppidBarCodeMap.getOrDefault(fai.getPpid(), Collections.emptyList());
                    fai.setBarCodeList(barCodeList);
                    fai.setLeftCount(leftCountMap.getOrDefault(fai.getPpid(), 0));
                })
                .sorted(Comparator.
                        <FilmAccessoriesV2Item, Boolean>comparing(item -> Objects.equals(item.getPpid(), productinfo.getPpriceid()), Comparator.reverseOrder())
                        .thenComparing(FilmAccessoriesV2Item::getLeftCount, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        return R.success(items);
    }

    /**
     * 排序
     *
     * @param data
     */
    private void sortData(List<FilmAccessoriesV2Item> data) {
        try {
            //判断只有九机做处理
            if (XtenantEnum.isJiujiXtenant() && CollectionUtils.isNotEmpty(data) && data.size()>NumberConstant.ONE) {
                //寻找出商品下市或下架，且门店库存为0排序靠后展示；
                for (int i = data.size() - 1; i >= 0; i--) {
                    FilmAccessoriesV2Item film = data.get(i);
                    Integer que = film.getQue();
                    Boolean display = film.getDisplay();
                    Integer leftCount = film.getLeftCount();
                    if (NumberConstant.ZERO.equals(leftCount) && (Boolean.FALSE.equals(display) || NumberConstant.TWO.equals(que))) {
                        FilmAccessoriesV2Item remove = data.remove(i);
                        data.add(remove);
                    }
                }
            }
        } catch (Exception e){
            RRExceptionHandler.logError("getFilmAccessoriesV3接口排序异常", data, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }

    }

    /**
     * 获取小件换货的默认配置信息
     *
     * @return
     */
    private List<SmallConfigDefaultPo> listSmallExchangeDefaultConfig() {
        String defaultConfigStr = ApolloKeys.getApolloProperty(ApolloKeys.SMALLPRO_ACCESSORIES_EXCHANGE_DEFAULT_CONFIG, null);
        if (StrUtil.isBlank(defaultConfigStr)) {
            return Collections.emptyList();
        }
        List<SmallConfigDefaultPo> smallConfigDefaults = JSON.parseArray(defaultConfigStr, SmallConfigDefaultPo.class);
        return ObjectUtil.defaultIfNull(smallConfigDefaults, Collections.emptyList());
    }

    @Override
    public R<ExchangeProductListVO> getExchangeProductList(SmallExchangeReq req) {
        ;
        // ppid校验
        Productinfo productinfo = productinfoService.getProductinfoByPpid(req.getPpid());
        if (productinfo == null) {

            return R.success(null);
        }
        String webKeyword = req.getKeywords();
        // key 为空 或相同的ppid
        boolean searchSamePpid = StrUtil.isBlank(webKeyword) || StrUtil.startWith(StrUtil.toString(req.getPpid()), webKeyword);
        // 查询小件兑换配置
        Optional<SmallExchangeConfigPo> configOpt = getConfigOpt(SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),
                productinfo, webKeyword, searchSamePpid);
        // 小件兑换商品
        ExchangeProductListVO exchangeProductListVo = configOpt
                .map(config -> getExchangeProductListVo(req, config, productinfo))
                // 找不到配置同ppid可互换
                .orElseGet(() -> {
                    ExchangeProductListVO exchangeListVo = new ExchangeProductListVO();
                    if (searchSamePpid) {
                        ExchangeProductListVO.ExchangeProduct ep = new ExchangeProductListVO.ExchangeProduct();
                        ep.setPpid(req.getPpid());
                        ep.setDifferentPrice(BigDecimal.ZERO);
                        List<ExchangeProductListVO.ExchangeProduct> products = Collections.singletonList(ep);

                        exchangeListVo.setProducts(products);
                        exchangeListVo.setPage(Long.valueOf(req.getPage()));
                        exchangeListVo.setSize(Long.valueOf(req.getSize()));
                        exchangeListVo.setPages(1L);
                        exchangeListVo.setTotal(1L);
                    }
                    return exchangeListVo;
                });

        return R.success(exchangeProductListVo);
    }

    @Override
    public Optional<SmallExchangeConfigPo> getConfigOpt(Integer serviceType, String key, Productinfo productinfo,
                                                        boolean searchSamePpid, Integer basketId) {
        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(currentRequestComponent.getCurrentStaffId());
        Optional<SmallExchangeConfigPo> configOpt = Arrays.stream(SmallExchangeConfigPo.ProductConfigTypeEnum.values())
                .sorted(Comparator.comparing(SmallExchangeConfigPo.ProductConfigTypeEnum::getRank))
                .map(configType -> smallProConfigService.getConfig(configType, serviceType, listConfigTypeAndValues(productinfo, configType),
                        Convert.toLong(key, 0L), key))
                .filter(Objects::nonNull)
                .findFirst().map(Optional::of)
                //key不为空 有高级权限 非相同ppid置换 构建高级搜索配置
                .orElseGet(() -> oaUserOpt.filter(oaUser -> StrUtil.isNotBlank(key) && CollUtil.contains(oaUser.getRank(), "xjzh") && !searchSamePpid)
                        .map(oaUser -> smallProConfigService.buildAdvancedExchangeConfig(serviceType)));

        if (CommenUtil.isNotNullZero(basketId) && configOpt.isPresent()) {
            SmallExchangeConfigPo config = configOpt.get();
            // 处理默认配置信息
            List<SmallConfigDefaultPo> defaultConfigList = listSmallExchangeDefaultConfig().stream()
                    .filter(dc -> CollUtil.contains(dc.getConfigIds(), config.getId())).collect(Collectors.toList());
            if (!defaultConfigList.isEmpty()) {
                LocalDateTime buyDate = CommenUtil.autoQueryHist(() -> basketService.getBuyDate(basketId));

                if (buyDate == null) {
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "购买时间为空,不获取默认值");
                } else {
                    defaultConfigList.stream().filter(defaultConfig -> buyDate.isBefore(defaultConfig.getEndTime()))
                            .forEach(defaultConfig -> {
                                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "{}时间之前,默认配置生效",
                                        defaultConfig.getEndTime().format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD_HH_MM_SS)));
                                SpringUtil.getBean(SmallproMapStruct.class).updateSmallExchangeConfig(defaultConfig.getDefaultConfig(), config);
                            });
                }
            }
        }
        return configOpt;
    }

    @Override
    public Optional<SmallExchangeConfigPo> getConfigOpt(Integer serviceType, Productinfo productinfo,
                                                        String webKeyword, boolean searchSamePpid) {

        return Arrays.stream(SmallExchangeConfigPo.ProductConfigTypeEnum.values())
                .sorted(Comparator.comparing(SmallExchangeConfigPo.ProductConfigTypeEnum::getRank))
                .map(configType -> smallProConfigService.getConfigForWeb(configType, serviceType,
                        listConfigTypeAndValues(productinfo, configType), webKeyword))
                .filter(Objects::nonNull)
                .findFirst()
                .map(Optional::of)
                .orElseGet(() -> StrUtil.isNotBlank(webKeyword) && !searchSamePpid
                        ? Optional.ofNullable(smallProConfigService.buildAdvancedExchangeConfig(serviceType))
                        : Optional.empty());
    }

    @Override
    public R<Boolean> batchReturnOperatorBasket(List<NotSoldBackBO> notSoldBackList) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (Objects.isNull(oaUserBO)) {
            return R.error("登陆失效！");
        }
        if (CollUtil.isEmpty(notSoldBackList)) {
            return R.error("参数错误！");
        }
        SysConfig sysConfig = sysConfigService.listAll().stream().filter(t -> t.getCode().equals(SysConfigConstant.MOA_URL)).findAny().orElse(null);
        String url = "";
        //https://moa.dev.9ji.com/commonApi/BatchReturnOperatorBasket'
        if (sysConfig != null && StringUtils.isNotEmpty(sysConfig.getValue())) {
            url = sysConfig.getValue() + "/" + "commonApi/BatchReturnOperatorBasket";
        }
        String ids = notSoldBackList.stream().map(NotSoldBackBO::getId).filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(","));
        if (StrUtil.isEmpty(ids)) {
            return R.error("参数错误！");
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        map.put("noCheckRank", 1);
        map.put("comment", "（绑定商品退款操作快捷返销）");
        String params = JSON.toJSONString(map);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", oaUserBO.getToken());
        try {
            String result = HttpClientUtil.post(url, params, headers);
            R r = JSON.parseObject(JSON.parse(result).toString(), R.class);
            if (r.getCode() == 0) {
                return R.success(Optional.ofNullable(r.getUserMsg()).orElse("批量返销成功！"));
            } else {
                log.error("批量返销失败，返回结果为：" + result);
                return R.error("批量返销失败，返回结果为：" + r.getUserMsg());
            }
        } catch (Exception e) {
            log.error("批量返销调用失败", e);
            return R.error("批量返销调用，返回结果为：" + e.getMessage());
        }
    }

    @Override
    public List<NotSoldBackBO> getProductIsResale(List<Integer> basketIdList) {
        if (CollUtil.isEmpty(basketIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.getProductIsResale(basketIdList);
    }

    @Override
    public R<List<NotSoldBackBO>> checkResultOperatorBasket(List<SmallproBill> smallproBillList) {
        //新增判断当前商品是否返销
        if (CollUtil.isEmpty(smallproBillList)) {
            return R.error("无数据");
        }
        //获取换货的商品列表
        if (CollUtil.isNotEmpty(smallproBillList)) {
            List<Integer> basketIdList = smallproBillList.stream().map(SmallproBill::getBasketId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
            //查询当前商品是否返销
            if (CollUtil.isNotEmpty(basketIdList)) {
                List<NotSoldBackBO> productIsResale = baseMapper.getProductIsResale(basketIdList);
                //根据basket查询ppid
                List<Integer> baskets = productIsResale.stream().map(NotSoldBackBO::getBasketId).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(baskets)) {
                    List<Basket> basketList = basketService.list(new LambdaQueryWrapper<Basket>().in(Basket::getBasketId, baskets)
                            .and(bo -> bo.eq(Basket::getIsdel, false).or().isNull(Basket::getIsdel)));
                    productIsResale.forEach(pr -> basketList.forEach(sm -> {
                        if (Objects.equals(pr.getBasketId(), sm.getBasketId())) {
                            pr.setPpid(sm.getPpriceid());
                        }
                    }));
                }
                productIsResale.forEach(pr -> smallproBillList.forEach(sm -> {
                    if (Objects.equals(pr.getBindBasketId(), sm.getBasketId())) {
                        pr.setBindPpid(sm.getPpriceid());
                    }
                }));
                return R.success(productIsResale);
            }
        }
        return R.success(null);
    }

    @Override
    public R cancelSmallTuifeiDiscount(String smallproID) {
        List<String> basketIdList = baseMapper.getBasketIdBySmallproID(smallproID);
        for (String str : basketIdList) {
            String key = "smallpro_not_discount_basket_" + str;
            redisTemplate.opsForValue().set(key, "true", 1, TimeUnit.DAYS);
        }
        return R.success("操作成功");
    }

    @Override
    public R<List<SmallYuyueOrderOnGoingVO>> listSmallYuyueOrderOnGoing(List<Integer> basketIds, Integer userId) {
        if (CollectionUtils.isEmpty(basketIds)) {

            return R.success(new ArrayList<>());
        }
        List<SmallYuyueOrderOnGoingVO> smallOrderCounts = baseMapper.listSmallOrderOnGoing(basketIds, userId);
        if (CollUtil.isEmpty(smallOrderCounts)) {
            smallOrderCounts = new ArrayList<>();
        }
        List<SmallYuyueOrderOnGoingVO> yuyueOrderCounts = baseMapper.listYuyueOrderOnGoing(basketIds, userId);
        if (CollUtil.isEmpty(yuyueOrderCounts)) {
            yuyueOrderCounts = new ArrayList<>();
        }
        List<SmallYuyueOrderOnGoingVO> finalYuyueOrderCounts = yuyueOrderCounts;
        List<SmallYuyueOrderOnGoingVO> finalSmallOrderCounts = smallOrderCounts;
        List<SmallYuyueOrderOnGoingVO> orderOnGoingVos = basketIds.stream()
                .map(basketId -> {
                    SmallYuyueOrderOnGoingVO orderOnGoingVo = new SmallYuyueOrderOnGoingVO();
                    orderOnGoingVo.setBasketId(basketId);
                    // 进行中的预约单量
                    Integer yuyueOrderCount = finalYuyueOrderCounts.stream()
                            .filter(item -> item.getBasketId().equals(basketId))
                            .findFirst()
                            .map(SmallYuyueOrderOnGoingVO::getYuyueOrderCount)
                            .orElse(IntConstant.ZERO);
                    orderOnGoingVo.setYuyueOrderCount(yuyueOrderCount);
                    // 进行中的小件单量
                    Integer smallOrderCount = finalSmallOrderCounts.stream()
                            .filter(item -> item.getBasketId().equals(basketId))
                            .findFirst()
                            .map(SmallYuyueOrderOnGoingVO::getSmallOrderCount)
                            .orElse(IntConstant.ZERO);
                    orderOnGoingVo.setSmallOrderCount(smallOrderCount);
                    return orderOnGoingVo;
                })
                .collect(Collectors.toList());
        return R.success(orderOnGoingVos);
    }

    /**
     * 设置当前ppid到第一位
     *
     * @param ppid
     * @param faList
     * @param key
     * @return
     */
    private List<FilmAccessoriesV2Item> setCurrentPpidFirst(Integer ppid, List<FilmAccessoriesV2Item> faList, String key) {
        //ppid 相等的放到第一个
        int i = 0;
        int size = faList.size();
        for (; i < size; i++) {
            FilmAccessoriesV2Item fa = faList.get(i);
            if (Objects.equals(fa.getPpid(), ppid)) {
                if (i > 0) {
                    FilmAccessoriesV2Item temp = faList.get(0);
                    faList.set(0, fa);
                    faList.set(i, temp);
                }
                break;
            }
        }
        return faList;
    }

    private FilmAccessoriesV2Item getFilmAccessoriesV2Item(Productinfo productinfo, Optional<OaUserBO> oaUserOpt) {
        FilmAccessoriesV2Item item = new FilmAccessoriesV2Item();
        item.setPpid(productinfo.getPpriceid());
        item.setProductName(productinfo.getProductName());
        item.setProductColor(productinfo.getProductColor());
        item.setDifference(BigDecimal.ZERO);
        Integer leftCount = oaUserOpt.map(oaUser -> baseMapper.getProductKcLeftCountByPpidAndAreaId(item.getPpid(), oaUser.getAreaId())).orElse(0);
        item.setLeftCount(leftCount);
        item.setBarCode(productinfo.getBarCode());
        item.setBarCodeList(SpringUtil.getBean(ProductbarcodeService.class).listBarCodeByPpids(Collections.singletonList(productinfo.getPpriceid()))
                .getOrDefault(productinfo.getPpriceid(), Collections.emptyList()));
        //只有九机才显示这两个字段
        if (XtenantEnum.isJiujiXtenant()) {
            item.setQue(productinfo.getQue())
                    .setDisplay(productinfo.getDisplay());
        }
        return item;
    }

    /**
     * 主站获取商品绑定贴膜
     * @param productIdList
     * @return
     */
    private List<WebConfigProductInfoRes> selectWebConfig(List<Integer> productIdList){
        List<WebConfigProductInfoRes> webConfigProductInfoRes = new ArrayList<>();
        if(CollectionUtils.isEmpty(productIdList)){
            return webConfigProductInfoRes;
        }
        String productIds = productIdList.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.WEB_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));

        String evidenceUrl = host + "/cloudapi_nc/web/api/open/listBindProductInfo?productIds=" + productIds;
        HttpResponse evidenceResult = HttpUtil.createGet(evidenceUrl)
                .header("xservicename", "web")
                .execute();
        log.warn("调用主站获取商品绑定贴膜接口传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult);
        if(evidenceResult.isOk()){
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(result.isSuccess()){
                Object data = result.getData();
                if(ObjectUtil.isNull(data)){
                    throw new CustomizeException("调用主站获取商品绑定贴膜为空");
                } else {
                    webConfigProductInfoRes = JSONUtil.toList(JSONUtil.toJsonStr(data), WebConfigProductInfoRes.class);
                }
            } else {
                throw new CustomizeException("调用主站获取商品绑定贴膜失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("调用主站获取商品绑定贴膜异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("调用主站获取商品绑定贴膜异常");
        }
        return webConfigProductInfoRes;
    }

    /**
     * 限制只能换绑定串号对应机型可贴膜
     * @param productInfoList
     * @param basketId
     * @param productinfo
     * @return
     */
    private List<Integer> selectRetainPpids(Integer basketId, Productinfo productinfo,String imei) {
        List<Integer> retainPpids = new ArrayList<>();
        return retainPpids;
//        try {
//            //如果是输出或者商品不是贴膜那就直接返回
//            Productinfo product = Optional.ofNullable(productinfo).orElse(new Productinfo());
//            if(XtenantEnum.isSaasXtenant() || !categoryService.tieMoCids().contains(product.getCid())){
//                return retainPpids;
//            }
//            retainPpids.add(productinfo.getPpriceid());
//            //更具basketId获取商品绑定串号
//            List<BasketBindRecord> basketBindRecordList = CommenUtil.autoQueryHist(()->basketBindRecordService.lambdaQuery()
//                    .eq(BasketBindRecord::getBasketId, basketId)
//                    .eq(StringUtils.isNotEmpty(imei),BasketBindRecord::getImei, imei)
//                    .list());
//            if(CollectionUtils.isEmpty(basketBindRecordList)){
//                return retainPpids;
//            }
//
//            List<Integer> productIdList = basketBindRecordList.stream()
//                    .map(item -> selectProductInfoListByImei(item.getImei()))
//                    .filter(CollectionUtils::isNotEmpty)
//                    .flatMap(item -> item.stream().map(ProductInfoListByImeiReq::getProductId))
//                    .filter(ObjectUtil::isNotNull).collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(productIdList)){
//                return retainPpids;
//            }
//            //通过主站接口获取ppid
//            List<WebConfigProductInfoRes> webConfigProductInfoRes = selectWebConfig(productIdList);
//            retainPpids.addAll(webConfigProductInfoRes.stream().flatMap(item -> item.getSkuIdList().stream()).distinct().collect(Collectors.toList()));
//            if(CollectionUtils.isNotEmpty(retainPpids)){
//                //获取贴膜分类
//                List<Integer> tieMoCids = categoryService.tieMoCids();
//                Map<Integer, Productinfo> productMap = productinfoService.getProductMapByPpids(retainPpids);
//                retainPpids=retainPpids.stream().filter(ppid->tieMoCids.contains(productMap.get(ppid).getCid())).collect(Collectors.toList());
//                retainPpids =  retainPpids.subList(NumberConstant.ZERO, retainPpids.size() >2000 ? 2000 : retainPpids.size());
//            }
//        }catch (Exception e){
//            RRExceptionHandler.logError("限制只能换绑定串号对应机型可贴膜异常", Dict.create().set("basketId",basketId).set("productinfo",productinfo)
//                    , e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
//        }
//        return retainPpids;
    }

    /**
     * 通过串号查询商品信息
     * @param imei
     * @return
     */
    @Override
    public ProductInfoListByImeiReq selectProductInfoListByImei(String imei){
        //良品库存进行查询
        IRecoverMkcService recoverMkcService = SpringUtil.getBean(IRecoverMkcService.class);
        Optional<RecoverMkc> recoverMkcOpt = CommenUtil.autoQueryHist(() -> recoverMkcService.lambdaQuery()
                .eq(RecoverMkc::getImei, imei)
                .orderByDesc(RecoverMkc::getId)
                .select(RecoverMkc::getId, RecoverMkc::getPpriceid)
                .isNotNull(RecoverMkc::getPpriceid)
                .list()).stream().findFirst();
        if(recoverMkcOpt.isPresent()){
            Productinfo productinfo = productinfoService.getProductinfoByPpid(recoverMkcOpt.get().getPpriceid());
            if(productinfo != null){
                return new ProductInfoListByImeiReq(productinfo.getProductId(), imei);
            }
        }
        //新机库存进行查询
        ProductMkcService productMkcService = SpringUtil.getBean(ProductMkcService.class);
        Optional<ProductMkc> productMkcOpt = CommenUtil.autoQueryHist(() -> productMkcService.lambdaQuery()
                .eq(ProductMkc::getImei, imei)
                .orderByDesc(ProductMkc::getId)
                .select(ProductMkc::getId, ProductMkc::getPpriceid)
                .isNotNull(ProductMkc::getPpriceid)
                .list()).stream().findFirst();
        if(productMkcOpt.isPresent()){
            Productinfo productinfo = productinfoService.getProductinfoByPpid(productMkcOpt.get().getPpriceid());
            if(productinfo != null){
                return new ProductInfoListByImeiReq(productinfo.getProductId(), imei);
            }
        }
        //通过三方接口使用imei查询productId
        ShouhouService shouhouService = SpringUtil.getBean(ShouhouService.class);
        ImeiQueryInfoBo imeiQueryInfoNotLimitUser = shouhouService.getImeiQueryInfoNotLimitUser(imei);
        if (ObjectUtil.isNotNull(imeiQueryInfoNotLimitUser)) {
            return imeiQueryInfoNotLimitUser.getProductInfo().stream().sorted(
                    Comparator.comparing(ImeiQueryInfoBo.ProductInfoItem::getProductId, Comparator.nullsLast(Comparator.naturalOrder())))
                    .map(item -> new ProductInfoListByImeiReq(item.getProductId(), imei))
                    .findFirst().orElse(null);
        }
        return null;
    }

    private List<FilmAccessoriesV2Item> getFilmAccessoriesV2Items(Integer basketId, String key, SmallExchangeConfigPo config,
                                                                  Productinfo productinfo, Integer mobileExchangeFlag,String imei) {
        List<Integer> retainPpids = selectRetainPpids(basketId, productinfo,imei);
        List<ProductExchangePo> productinfos = listProductinfos(config, Convert.toLong(key, 0L), retainPpids, key);
        return getFilmAccessoriesV2Items(basketId, key, config, productinfo, mobileExchangeFlag, productinfos);
    }

    private List<FilmAccessoriesV2Item> getFilmAccessoriesV2Items(Integer basketId, String key, SmallExchangeConfigPo config,
                                                                  Productinfo productinfo, Integer mobileExchangeFlag,
                                                                  List<ProductExchangePo> productinfos) {

        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(currentRequestComponent.getCurrentStaffId());
        //不包含相同ppid数据,进行追加
        if ((StrUtil.isBlank(key) || StrUtil.startWith(StrUtil.toString(productinfo.getPpriceid()), key))
                && !Boolean.TRUE.equals(productinfo.getIsmobile1())
                && productinfos.stream().noneMatch(pi -> Objects.equals(pi.getPpriceid(), productinfo.getPpriceid()))) {
            Integer leftCount = oaUserOpt.map(oaUser -> baseMapper.getProductKcLeftCountByPpidAndAreaId(productinfo.getPpriceid(), oaUser.getAreaId())).orElse(0);
            ProductExchangePo productExchangePo = ProductExchangePo.wrap(productinfo, leftCount);
            productinfos.add(productExchangePo);
        }
        String diffPriceTypeMessage = EnumUtil.getMessageByCode(SmallExchangeConfigPo.DifferentPriceTypeEnum.class, config.getDifferentPriceType());
        //计算差价
        Tuple2<BigDecimal, String> buyPriceTuple = getBuyPrice(basketId, config, mobileExchangeFlag);
        //购买时的商品金额 或者服务金额
        AtomicReference<BigDecimal> buyPriceRef = new AtomicReference<>(buyPriceTuple.getT1());
        AtomicReference<String> priceNameRef = new AtomicReference<>(buyPriceTuple.getT2());

        List<Integer> ppids = productinfos.stream().map(ProductExchangePo::getPpriceid).collect(Collectors.toList());
        Map<Integer, List<String>> productBarcodeMap = SpringUtil.getBean(ProductbarcodeService.class)
                .listBarCodeByPpids(ppids);
        return productinfos.stream()
                .map(pinfo -> {
                    BigDecimal memberPrice = ObjectUtil.defaultIfNull(pinfo.getMemberprice(), BigDecimal.ZERO);
                    Tuple2<BigDecimal, String> differenceTuple = calculateDifference(config, buyPriceRef.get(), memberPrice);
                    BigDecimal difference = differenceTuple.getT1();
                    boolean isSamePpriceIdIgnoreDiff = difference.compareTo(BigDecimal.ZERO) > 0
                            && Objects.equals(pinfo.getPpriceid(), productinfo.getPpriceid());
                    boolean isAdvance = Objects.equals(config.getId(), SmallProConfigService.ADVANCE_RANK_CONFIG_ID);
                    String advanceMsg = DecideUtil.iif(isAdvance, "高级授权xjzh", "");
                    boolean isSamePpidIgnoreMsg = !NumberConstant.ONE.equals(mobileExchangeFlag) && isSamePpriceIdIgnoreDiff;
                    FilmAccessoriesV2Item filmAccessoriesV2Item = new FilmAccessoriesV2Item().setConfigId(config.getId()).setBarCode(pinfo.getBarCode())
                            .setMemberPrice(pinfo.getMemberprice())
                            .setLeftCount(pinfo.getLeftCount())
                            .setProductName(pinfo.getProductName()).setBarCode(pinfo.getBarCode())
                            .setBarCodeList(productBarcodeMap.getOrDefault(pinfo.getPpriceid(),new ArrayList<>()))
                            .setProductColor(pinfo.getProductColor())
                            .setDifference(DecideUtil.iif(isSamePpriceIdIgnoreDiff, BigDecimal.ZERO, difference))
                            .setBuyPrice(buyPriceRef.get())
                            .setIsMakeUpPrice(config.getIsDifferentPrice()).setMakeUpPricePercent(config.getDifferentPricePercent())
                            .setMakeUpPriceWay(config.getDifferentPriceType()).setMakeUpPriceWayText(diffPriceTypeMessage)
                            .setAdvanceMessage(advanceMsg)
                            .setDifferenceText(DecideUtil.iif(isSamePpidIgnoreMsg, "同ppid忽略差价", differenceTuple.getT2()))
                            .setMassage(getItemMessage(priceNameRef.get(), buyPriceRef.get(), memberPrice, isAdvance,
                                    advanceMsg, isSamePpidIgnoreMsg, difference))
                            .setMemberPrice(pinfo.getMemberprice())
                            .setPpid(pinfo.getPpriceid())
                            .setSubtractPrice(memberPrice.subtract(buyPriceRef.get()));
                    //只有九机才显示这两个字段
                    if (XtenantEnum.isJiujiXtenant()) {
                        filmAccessoriesV2Item.setQue(pinfo.getQue())
                                .setDisplay(pinfo.getDisplay());
                    }
                    return filmAccessoriesV2Item;
                })
                .collect(Collectors.toList());
    }

    private static String getItemMessage(String priceName, BigDecimal buyPrice,
                                         BigDecimal memberPrice, boolean isAdvance, String advanceMsg,
                                         boolean isSamePpidIgnoreMsg, BigDecimal difference) {
        return StrUtil.format("{} {}，置换商品价格 {}，<span style=\"color: red;\">{}{}差价金额 {}</span>",
                priceName, buyPrice, memberPrice, DecideUtil.iif(isAdvance, StrUtil.format("[{}]", advanceMsg), ""),
                DecideUtil.iif(isSamePpidIgnoreMsg, "同ppid忽略", "需补"),
                difference);
    }

    private Tuple2<BigDecimal, String> getBuyPrice(Integer basketId, SmallExchangeConfigPo config, Integer mobileExchangeFlag) {
        AtomicReference<String> priceNameRef = new AtomicReference<>("");
        AtomicReference<BigDecimal> buyPriceRef = new AtomicReference<>(BigDecimal.ZERO);
        if (Boolean.TRUE.equals(config.getIsDifferentPrice())) {
            if (Boolean.TRUE.equals(config.getIsServiceDifferentPrice())
                    && !Objects.equals(config.getServiceType(), SmallProServiceTypeEnum.SMALL_PRO_KIND_NULL.getCode())) {
                //服务类型的
                priceNameRef.set("服务价格");
                Optional.ofNullable(getFilmCardBasket(basketId)).map(Basket::getPrice).ifPresent(buyPriceRef::set);
            } else {
                priceNameRef.set("购买商品价格");
                Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.getByIdSqlServer(basketId), MTableInfoEnum.BASKET, basketId))
                        .map(Basket::getPrice1).ifPresent(buyPriceRef::set);
            }
            if (NumberConstant.ONE.equals(mobileExchangeFlag)) {
                priceNameRef.set("置换补贴");
                //大件商品附件,取配置中的固定金额
                buyPriceRef.set(new BigDecimal(ApolloKeys.getApolloProperty(ApolloKeys.MOBILE_ACCESSORIES_EXCHANGE_PRICE, "200")));
            }
        }
        return Tuples.of(buyPriceRef.get(), priceNameRef.get());
    }

    private ExchangeProductListVO getExchangeProductListVo(SmallExchangeReq req, SmallExchangeConfigPo config, Productinfo productinfo) {
        ExchangeProductListVO exchangeProductListVo = new ExchangeProductListVO();
        // 分页查询商品数据
        Page<ProductExchangePo> page = pageExchangeProductinfos(config, req, exchangeProductListVo);
        // 首页
        boolean isFirstPage = req.getPage().equals(IntConstant.ONE);
        List<ProductExchangePo> productinfos = page.getRecords();
        // 不包含相同ppid数据,进行追加
        if (isFirstPage && productinfos.stream().noneMatch(pi -> Objects.equals(pi.getPpriceid(), productinfo.getPpriceid()))) {
            ProductExchangePo productExchangePo = ProductExchangePo.wrap(productinfo, 0);
            productinfos.add(productExchangePo);
        }
        // 计算差价
        // 购买时的商品金额 或者服务金额
        AtomicReference<BigDecimal> buyPriceRef = new AtomicReference<>(BigDecimal.ZERO);
        if (Boolean.TRUE.equals(config.getIsDifferentPrice())) {
            if (Boolean.TRUE.equals(config.getIsServiceDifferentPrice()) && !Objects.equals(config.getServiceType(), SmallProServiceTypeEnum.SMALL_PRO_KIND_NULL.getCode())) {
                //服务类型的
                Optional.ofNullable(getFilmCardBasket(req.getBasketId()))
                        .map(Basket::getPrice)
                        .ifPresent(buyPriceRef::set);
            } else {
                Optional.ofNullable(basketService.getByIdSqlServer(req.getBasketId()))
                        .map(Optional::of)
                        .orElseGet(() -> Optional.ofNullable(CommenUtil.autoQueryHist(() -> SpringUtil.getBean(BasketService.class).getByIdSqlServer(req.getBasketId()), MTableInfoEnum.BASKET, req.getBasketId())))
                        .map(Basket::getPrice1)
                        .ifPresent(buyPriceRef::set);
            }
        }

        List<ExchangeProductListVO.ExchangeProduct> products = productinfos.stream()
                .map(pinfo -> {
                    // 会员价
                    BigDecimal memberPrice = ObjectUtil.defaultIfNull(pinfo.getMemberprice(), BigDecimal.ZERO);
                    // 差价
                    Tuple2<BigDecimal, String> differenceTuple = calculateDifference(config, buyPriceRef.get(), memberPrice);
                    BigDecimal difference = differenceTuple.getT1();
                    return new ExchangeProductListVO.ExchangeProduct()
                            .setPpid(pinfo.getPpriceid())
                            .setDifferentPrice(difference);
                })
                .collect(Collectors.toList());
        if (isFirstPage && CollectionUtils.isNotEmpty(products)) {
            // ppid 相等的放到第一个
            for (int i = 0; i < products.size(); i++) {
                ExchangeProductListVO.ExchangeProduct exchangeProduct = products.get(i);
                if (Objects.equals(exchangeProduct.getPpid(), req.getPpid())) {
                    if (i > 0) {
                        ExchangeProductListVO.ExchangeProduct temp = products.get(0);
                        products.set(0, exchangeProduct);
                        products.set(i, temp);
                    }
                    break;
                }
            }
        }

        exchangeProductListVo.setProducts(products);

        return exchangeProductListVo;
    }

    private Tuple2<BigDecimal, String> calculateDifference(SmallExchangeConfigPo config, BigDecimal buyPrice, BigDecimal memberPrice) {
        BigDecimal computePrice = BigDecimal.ZERO;
        StringJoiner textJoiner = new StringJoiner(" ");
        if (Objects.equals(SmallExchangeConfigPo.DifferentPriceTypeEnum.DIFFERENT_PRICE.getCode(), config.getDifferentPriceType())) {
            //价格差
            computePrice = memberPrice.subtract(buyPrice);
            textJoiner.add(StrUtil.format("(¥{}(置换商品售价) - ¥{}(原商品售价))", memberPrice, buyPrice));
        } else if (Objects.equals(SmallExchangeConfigPo.DifferentPriceTypeEnum.EXCHANGE_PRICE.getCode(), config.getDifferentPriceType())) {
            //商品价格
            computePrice = memberPrice;
            textJoiner.add(StrUtil.format("¥{}(置换商品售价)", memberPrice));
        }
        //计算价差
        BigDecimal differentPricePercent = ObjectUtil.defaultIfNull(config.getDifferentPricePercent(), new BigDecimal(100L));
        BigDecimal result = Optional.ofNullable(computePrice.multiply(differentPricePercent)
                .divide(BigDecimal.valueOf(100L), 2, BigDecimal.ROUND_HALF_UP))
                .filter(di -> di.compareTo(BigDecimal.ZERO) >= 0).orElse(BigDecimal.ZERO);
        textJoiner.add(StrUtil.format("* {}%(补差价百分比)", differentPricePercent));
        return Tuples.of(result, textJoiner.toString());
    }

    private List<ProductExchangePo> listProductinfos(SmallExchangeConfigPo config, Long wordKey, List<Integer> retainPpids,String key) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        List<SmallExchangeProductConfigPo> productConfigPos = smallProConfigService.listConfigProduct(config.getId());
        List<ProductExchangePo> result = new ArrayList<>();
        Map<Integer, List<SmallExchangeProductConfigPo>> productConfigMap = productConfigPos.stream().
                collect(Collectors.groupingBy(SmallExchangeProductConfigPo::getConfigType));
        for(Integer productConfigType : productConfigMap.keySet()) {
            SmallExchangeConfigPo.ProductConfigTypeEnum configType = Optional.ofNullable(productConfigType)
                    .map(st -> EnumUtil.getEnumByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, st))
                    .orElse(null);
            List<SmallExchangeProductConfigPo> smallExchangeProductConfigPos = productConfigMap.get(productConfigType);
            if (configType != null && CollUtil.isNotEmpty(smallExchangeProductConfigPos)) {
                result.addAll(getProductExchangePos(wordKey, configType, smallExchangeProductConfigPos, oaUser, retainPpids, key));
            }
        }
        return result;
    }

    private List<ProductExchangePo> getProductExchangePos(Long wordKey, SmallExchangeConfigPo.ProductConfigTypeEnum configType,
                                                          List<SmallExchangeProductConfigPo> productConfigPos, OaUserBO oaUser,
                                                          List<Integer> retainPpids, String key) {
        List<ProductExchangePo> result = new ArrayList<>();
        switch (configType) {
            case SKU:
                result = CommonUtils.bigDataInQuery(productConfigPos.stream().map(SmallExchangeProductConfigPo::getConfigValue)
                                .filter(Objects::nonNull).collect(Collectors.toList()),
                        ppids -> smallProConfigService.listSmallProductInfo(new SmallConfigProductInfoPo()
                                .setKcAreaId(oaUser.getAreaId()).setPpids(ppids).setRetainPpids(retainPpids), wordKey, key));
                break;
            case SPU:
                result = CommonUtils.bigDataInQuery(productConfigPos.stream().map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).collect(Collectors.toList()), productIds ->
                        smallProConfigService.listSmallProductInfo(new SmallConfigProductInfoPo().setKcAreaId(oaUser.getAreaId())
                                .setProductIds(productIds).setRetainPpids(retainPpids), wordKey, key));
                break;
            case BRAND_CATEGORY:
                SmallConfigProductInfoPo smallConfigProductInfo = new SmallConfigProductInfoPo().setKcAreaId(oaUser.getAreaId()).setRetainPpids(retainPpids);
                smallConfigProductInfo.setCids(productConfigPos.stream()
                        .filter(sepc -> Objects.equals(sepc.getConfigType(), SmallExchangeProductConfigPo.ConfigTypeEnum.CATEGORY.getCode()))
                        .map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                smallConfigProductInfo.setBrandIds(productConfigPos.stream()
                        .filter(sepc -> Objects.equals(sepc.getConfigType(), SmallExchangeProductConfigPo.ConfigTypeEnum.BRAND.getCode()))
                        .map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                result = smallProConfigService.listSmallProductInfo(smallConfigProductInfo, wordKey, key);
                break;
            case CATEGORY:
                result = CommonUtils.bigDataInQuery(productConfigPos.stream().map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()), cids ->
                        smallProConfigService.listSmallProductInfo(new SmallConfigProductInfoPo().setKcAreaId(oaUser.getAreaId())
                                .setCids(cids).setRetainPpids(retainPpids), wordKey, key));
                break;
            case BRAND:
                result = CommonUtils.bigDataInQuery(productConfigPos.stream().map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()), brandIds ->
                        smallProConfigService.listSmallProductInfo(new SmallConfigProductInfoPo().setKcAreaId(oaUser.getAreaId())
                                .setBrandIds(brandIds).setRetainPpids(retainPpids), wordKey, key));
                break;
        }
        return result;
    }

    private Page<ProductExchangePo> pageExchangeProductinfos(SmallExchangeConfigPo config, SmallExchangeReq req,
                                                             ExchangeProductListVO exchangeProductListVo) {
        SmallExchangeConfigPo.ProductConfigTypeEnum configType = Optional.ofNullable(config.getProductConfigType())
                .map(st -> EnumUtil.getEnumByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, st))
                .orElseThrow(() -> new CustomizeException("不可能发生,配置类型不存在"));
        List<SmallExchangeProductConfigPo> productConfigPos = smallProConfigService.listConfigProduct(config.getId());
        SmallConfigProductInfoPo productInfoPo = new SmallConfigProductInfoPo();
        switch (configType) {
            case SKU:
                List<Integer> ppids = productConfigPos.stream()
                        .map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                productInfoPo.setPpids(ppids);
                break;
            case SPU:
                List<Integer> productIds = productConfigPos.stream()
                        .map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                productInfoPo.setProductIds(productIds);
                break;
            case BRAND_CATEGORY:
                productInfoPo.setCids(productConfigPos.stream()
                        .filter(sepc -> Objects.equals(sepc.getConfigType(), SmallExchangeProductConfigPo.ConfigTypeEnum.CATEGORY.getCode()))
                        .map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                productInfoPo.setBrandIds(productConfigPos.stream()
                        .filter(sepc -> Objects.equals(sepc.getConfigType(), SmallExchangeProductConfigPo.ConfigTypeEnum.BRAND.getCode()))
                        .map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                break;
            case CATEGORY:
                List<Integer> cIds = productConfigPos.stream()
                        .map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                productInfoPo.setCids(cIds);
                break;
            case BRAND:
                List<Integer> brandIds = productConfigPos.stream()
                        .map(SmallExchangeProductConfigPo::getConfigValue)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                productInfoPo.setBrandIds(brandIds);
                break;
            default:
                break;
        }
        // 查询总条目
        int withWebCount = smallProConfigService.countSmallProductInfo(productInfoPo, req);
        int withWebPages = withWebCount / req.getSize();
        if (withWebCount % req.getSize() != 0) {
            withWebPages++;
        }
        int noWebCount = IntConstant.ZERO;
        int noWebPages = IntConstant.ZERO;
        if (CollectionUtils.isNotEmpty(req.getWebPpids())) {
            req.setWebPpidsNotIn(true);
            noWebCount = smallProConfigService.countSmallProductInfo(productInfoPo, req);
            noWebPages = noWebCount / req.getSize();
            if (noWebCount % req.getSize() != 0) {
                noWebPages++;
            }
        }

        int pages = withWebPages + noWebPages;
        int total = withWebCount + noWebCount;

        exchangeProductListVo.setPage(Long.valueOf(req.getPage()));
        exchangeProductListVo.setSize(Long.valueOf(req.getSize()));
        exchangeProductListVo.setPages((long) pages);
        exchangeProductListVo.setTotal((long) total);

        Page<ProductExchangePo> page = new Page<>(req.getPage(), req.getSize(), total);
        page.setRecords(new ArrayList<>());
        if (total <= 0 || req.getPage() > pages) {

            return page;
        }
        req.setWebPpidsNotIn(false);
        if (req.getPage() > withWebPages) {
            // 分页查询 offset
            Integer noWeboffset = (req.getPage() - withWebPages - 1) * req.getSize();
            req.setOffset(noWeboffset);
            req.setWebPpidsNotIn(true);
        }
        // 分页查询商品列表
        List<ProductExchangePo> records = smallProConfigService.listSmallProductInfo(productInfoPo, req);
        if (CollectionUtils.isNotEmpty(records)) {
            page.setRecords(records);
        }
        return page;
    }

    /**
     * 获取配置列表
     *
     * @param productinfo
     * @param configType
     * @return
     */
    private List<SmallExchangeConfigPo.ConfigTypeAndValue> listConfigTypeAndValues(Productinfo productinfo,
                                                                                   SmallExchangeConfigPo.ProductConfigTypeEnum configType) {
        List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues = new LinkedList<>();
        switch (configType) {
            case SKU:
                configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                        .setConfigType(configType).setConfigValue(productinfo.getPpriceid()));
                break;
            case SPU:
                configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                        .setConfigType(configType).setConfigValue(productinfo.getProductId()));
                break;
            case BRAND_CATEGORY:
                //品牌查询直接返回查询结果
                configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                        .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND).setConfigValue(productinfo.getBrandID()));
                configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                        .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY).setConfigValue(productinfo.getCid()));
                break;
            case CATEGORY:
                configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                        .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY).setConfigValue(productinfo.getCid()));
                break;
            case BRAND:
                configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                        .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND).setConfigValue(productinfo.getBrandID()));
                break;
        }
        return configTypeAndValues;
    }

    /**
     * 处理保护壳半价复购提醒推送
     */
    @Override
    public void handlePhoneCasePurchaseBuy() {
        //条件：购买商品为可半价复购手机壳（壳分类id43，分类树）
        //时间：商品交易完成后次日19点推送短信
        LocalDate now = LocalDate.now();
        LocalDateTime endTime = now.atStartOfDay();
        LocalDateTime startTime = now.minusDays(1).atStartOfDay();
        List<String> mobileList1 = basketService.getMobileListPurchaseBuy1(startTime,endTime);
        if (CollUtil.isNotEmpty(mobileList1)) {
            String mobiles = String.join(",", mobileList1);
            smsService.sendSms(mobiles,"“九机会员专享”亲~手机壳半价复购权益已发放至您账户，可在商城APP/小程序-服务-我的壳膜中查看适配产品哦~",DateUtil.localDateTimeToString(LocalDateTime.now()),"系统",commonService.getSmsChannelByXtenant(0L, ESmsChannelTypeEnum.YXTD.getCode()),"");
        }
        //条件：最后一笔手机壳订单距今前60、120、180天（准确等于这个时间），并且此订单不是年包兑换，不是半价复购的订单；
        //时间：每天19点滚动触达;
        List<String> mobileList2 = basketService.getMobileListPurchaseBuy2();
        if (CollUtil.isNotEmpty(mobileList2)) {
            String mobiles = String.join(",", mobileList2);
            smsService.sendSms(mobiles,"“九机会员专享”亲~您享有九机半价复购手机壳权益。换壳如换机！快来挑选心仪的福利好物吧！9ji.cn/oanInb",DateUtil.localDateTimeToString(LocalDateTime.now()),"系统",commonService.getSmsChannelByXtenant(0L, ESmsChannelTypeEnum.YXTD.getCode()),"");
        }
    }

    /**
     * 不折价退款申请
     *
     * @param req
     * @return
     */
    @Override
    public R<Boolean> submitNoDiscount(SmallproNoDiscountReqVo req) {
        String key = StrUtil.format(RedisKeys.SERVICE_NOT_DISCOUNT_REFUND_KEY, req.getSmallproId());
        OaUserBO oaUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        req.setOperateDate(LocalDateTime.now());
        req.setInUser(oaUser.getUserName());
        String jsonStr = JSON.toJSONString(req);
        stringRedisTemplate.opsForValue().set(key, jsonStr, 1, TimeUnit.DAYS);
        String logMsg = StrUtil.format("提交服务不折价退款申请，原因：{}", req.getReason());
        //记录日志
        smallproLogService.addLogs(req.getSmallproId(), logMsg, oaUser.getUserName(), 0);
        return R.success(true);
    }

    /**
     * 不折价退款审核
     *
     * @param req
     * @return
     */
    @Override
    public R<Boolean> checkNoDiscount(SmallproNoDiscountCheckReqVo req) {
        OaUserBO oaUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        // 获取小件订单关联basketId
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, req.getSmallproId());
        List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        if (CollUtil.isEmpty(smallproBillList)) {
            return R.error("小件单商品不存在");
        }
        if (Objects.equals(1, req.getStatus())) {
            SmallproDetailsExService smallproDetailsExService = SpringUtil.getBean(SmallproDetailsExService.class);
            for(SmallproBill smallproBill : smallproBillList) {
                boolean isServiceProduct = smallproDetailsExService.isServiceProduct(smallproBill.getBasketId(), Convert.toInt(smallproBill.getPpriceid())) || smallproDetailsExService.isYearCardProduct(Convert.toInt(smallproBill.getPpriceid()));
                if (isServiceProduct) {
                    //小件无折扣退款
                    stringRedisTemplate.opsForValue().set(StrUtil.format(RedisKeys.SMALLPRO_NOT_DISCOUNT_BASKET_KEY, smallproBill.getBasketId()), "1", 1, TimeUnit.DAYS);
                }
            }
        }
        String logMsg = StrUtil.format("服务不折价退款，审批{}，审批备注：{}", req.getStatus() == 1 ? "已通过" : "已拒绝", req.getRemark());
        //记录日志
        smallproLogService.addLogs(req.getSmallproId(), logMsg, oaUser.getUserName(), 0);
        String key = StrUtil.format(RedisKeys.SERVICE_NOT_DISCOUNT_REFUND_KEY, req.getSmallproId());
        stringRedisTemplate.delete(key);
        return R.success(true);
    }

    /**
     * 获取不折价退款申请单详情
     *
     * @param smallProId
     * @return
     */
    @Override
    public R<SmallproNoDiscountResVo> getSubmitNoDiscount(Integer smallProId) {
        String key = StrUtil.format(RedisKeys.SERVICE_NOT_DISCOUNT_REFUND_KEY, smallProId);
        String jsonStr = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isNotBlank(jsonStr)) {
            SmallproNoDiscountResVo resVo = JSON.parseObject(jsonStr, SmallproNoDiscountResVo.class);
            return R.success(resVo);
        }
        return R.success(new SmallproNoDiscountResVo());
    }
}

package com.jiuji.oa.afterservice.sys.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.tenant.Namespaces;
import com.jiuji.oa.afterservice.bigpro.dao.BbsxpUsersMapper;
import com.jiuji.oa.afterservice.bigpro.service.NumberCardService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.sys.dao.SysconfigLogMapper;
import com.jiuji.oa.afterservice.sys.enums.ConfigEnum;
import com.jiuji.oa.afterservice.sys.enums.OperationTypeEnum;
import com.jiuji.oa.afterservice.sys.po.SysConfig;
import com.jiuji.oa.afterservice.sys.po.SysconfigLog;
import com.jiuji.oa.afterservice.sys.service.SysconfigLogService;
import com.jiuji.oa.afterservice.sys.vo.res.BbsxpUsersVO;
import com.jiuji.oa.afterservice.sys.vo.res.RecycleFollowNewSubConfigVO;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/10/26 14:13
 * @Description
 */
@Slf4j
@Service
public class SysconfigLogServiceImpl extends ServiceImpl<SysconfigLogMapper, SysconfigLog> implements SysconfigLogService {

    private static final Integer COMMENT_END_LENGTH = 2048;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private NumberCardService numberCardService;
    @Resource
    private BbsxpUsersMapper usersMapper;
    /**
     * 增加配置项
     *
     * @param sysConfigList sysConfigList
     * @param list          list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SysConfig> addSysConfig(List<SysConfig> sysConfigList, List<SysConfig> list, OaUserBO user) {
        List<String> addSysList = list.stream().filter(it -> CommenUtil.isNullOrZero(it.getId())).map(SysConfig::getName)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addSysList)) {
            return Collections.emptyList();
        }
        List<SysconfigLog> sysConfigLogList = new ArrayList<>(addSysList.size());
        List<SysConfig> addSysConfigList = new ArrayList<>(addSysList.size());
        addSysList.forEach(it -> addSysConfigByName(list, sysConfigLogList, addSysConfigList, it, user));
        if (!CollectionUtils.isEmpty(sysConfigLogList)) {
            this.baseMapper.sqlServerBatchInsert(sysConfigLogList);
        }
        return addSysConfigList;
    }

    /**
     * getSysConfigById
     *
     * @param list             list
     * @param sysConfigLogList sysConfigLogList
     * @param addSysConfigList addSysConfigList
     * @param name             name
     * @param user             user
     */
    private void addSysConfigByName(List<SysConfig> list, List<SysconfigLog> sysConfigLogList
            , List<SysConfig> addSysConfigList, String name, OaUserBO user) {
        Optional<SysConfig> optSysConfig = list.stream().filter(it -> it.getName().equals(name)).findFirst();
        if (optSysConfig.isPresent()) {
            SysConfig sysConfig = optSysConfig.get();
            SysconfigLog sysConfigLog = new SysconfigLog().setCode(sysConfig.getCode())
                    .setOperationType(OperationTypeEnum.SYSCONFIG_SAVE.getCode());
            String comment = "新增配置：" + ConfigEnum.getMessage(sysConfig.getCode()) + "，名称:" + sysConfig
                    .getName() + ",值:" + getSysConfigValueDesc(sysConfig);
            // 如果是【回收加单免锁单账号配置】，日志特殊处理
            if (Objects.equals(sysConfig.getCode(),ConfigEnum.RECYCLE_UNLOCK_CONFIG.getCode()) && StringUtil.isNotBlank(sysConfig.getValue())){
                List<BbsxpUsersVO> userList = usersMapper.getSimpleUser(Arrays.asList(sysConfig.getValue().split(",")), Namespaces.get());
                if (!CollectionUtils.isEmpty(userList)){
                    StringBuilder sb = new StringBuilder();
                    sb.append("新增配置:");
                    for (BbsxpUsersVO bbsxpUsersVO : userList) {
                        sb.append(bbsxpUsersVO.getMobile()).append("(")
                                .append(StringUtils.isEmpty(bbsxpUsersVO.getCh999Name()) ? "会员:"+bbsxpUsersVO.getUserName() : "员工:"+bbsxpUsersVO.getCh999Name())
                                .append(")");
                    }
                    comment = sb.toString();
                }
            }
            sysConfigLog.setComment(comment);
            sysConfigLog.setInUserId(user.getUserId());
            sysConfigLog.setInUserName(user.getUserName());
            sysConfigLogList.add(sysConfigLog);
            addSysConfigList.add(sysConfig);
        }
    }

    /**
     * 更新配置项
     *
     * @param sysConfigList sysConfigList
     * @param list          list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SysConfig> editSysConfig(List<SysConfig> sysConfigList, List<SysConfig> list, OaUserBO user) {
        List<Integer> retainList = list.stream().map(SysConfig::getId).collect(Collectors.toList());
        //求交集
        retainList.retainAll(sysConfigList.stream().map(SysConfig::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(retainList)) {
            return Collections.emptyList();
        }
        List<SysconfigLog> sysConfigLogList = new ArrayList<>(retainList.size());
        List<SysConfig> editSysConfigList = new ArrayList<>(retainList.size());
        retainList.forEach(it -> editSysConfigById(list, sysConfigList, sysConfigLogList, editSysConfigList, it, user));
        if (!CollectionUtils.isEmpty(sysConfigLogList)) {
            this.baseMapper.sqlServerBatchInsert(sysConfigLogList);
        }
        return editSysConfigList;
    }

    /**
     * editSysConfigByName
     *
     * @param list              list
     * @param sysConfigLogList  sysConfigLogList
     * @param editSysConfigList editSysConfigList
     * @param id                id
     */
    private void editSysConfigById(List<SysConfig> list, List<SysConfig> sysConfigList, List<SysconfigLog> sysConfigLogList,
                                   List<SysConfig> editSysConfigList, Integer id, OaUserBO user) {
        Optional<SysConfig> optNewSysConfig = list.stream().filter(it -> it.getId().equals(id)).findFirst();
        Optional<SysConfig> optOriginSysConfig = sysConfigList.stream().filter(it -> it.getId().equals(id)).findFirst();
        if (optNewSysConfig.isPresent() && optOriginSysConfig.isPresent()) {
            SysConfig newSysConfig = optNewSysConfig.get();
            SysConfig originSysConfig = optOriginSysConfig.get();
            String originName = Optional.ofNullable(originSysConfig.getName()).orElse("").trim();
            String originValue = getSysConfigValueDesc(originSysConfig);
            String newName = Optional.ofNullable(newSysConfig.getName()).orElse("").trim();
            String newValue = getSysConfigValueDesc(newSysConfig);

            if (!originName.equals(newName) || !originValue.equals(newValue)) {
                SysconfigLog sysConfigLog = new SysconfigLog().setCode(newSysConfig.getCode())
                        .setOperationType(OperationTypeEnum.SYSCONFIG_UPDATE.getCode());
                sysConfigLog.setConfigId(originSysConfig.getId());
                String comment = "更新配置：" + ConfigEnum.getMessage(newSysConfig.getCode());
                if (!originName.equals(newName)) {
                    comment +="，旧名称:" + originName + ",;新名称:" + newName;
                }
                if (!originValue.equals(newValue)) {
                    comment +="，旧值:" + originValue + ",;新值:" + newValue;
                }
                //报表剔除跨授权调拨订单
                List<Integer> switchConfigList = Arrays.asList(ConfigEnum.REPORT_EXCLUDES_CROSS_AUTHORIZED_TRANSFER_ORDERS.getCode(), ConfigEnum.NATIONAL_USER_ADDRESS.getCode());

                if (switchConfigList.contains(Optional.ofNullable(newSysConfig.getCode()).orElse(0))){
                    if (!originValue.equals(newValue)) {
                        comment = StrUtil.format("{}【{}】配置", Objects.equals("1",newValue) ? "开启" : "关闭", ConfigEnum.getMessage(newSysConfig.getCode()));
                    }
                }

                // 如果是【回收加单免锁单账号配置】，日志特殊处理
                if (Objects.equals(newSysConfig.getCode(),ConfigEnum.RECYCLE_UNLOCK_CONFIG.getCode())){
                    StringBuilder sb = new StringBuilder();
                    sb.append("更新配置:");
                    List<BbsxpUsersVO> oldUserList = null,newUserList = null;
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(originValue)){
                        oldUserList =  usersMapper.getSimpleUser(Arrays.asList(originValue.split(",")),Namespaces.get());
                    }
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(newValue)){
                        newUserList =  usersMapper.getSimpleUser(Arrays.asList(newValue.split(",")),Namespaces.get());
                    }
                    if (!CollectionUtils.isEmpty(oldUserList)){
                        sb.append("旧值：");
                        List<String> temp = new ArrayList<>();
                        for (BbsxpUsersVO bbsxpUsersVO : oldUserList) {
                            String tempBuilder = bbsxpUsersVO.getMobile() + "(" +
                                    (StringUtils.isEmpty(bbsxpUsersVO.getCh999Name()) ? "会员:" + bbsxpUsersVO.getUserName() : "员工:" + bbsxpUsersVO.getCh999Name()) +
                                    ")";
                            temp.add(tempBuilder);
                        }
                        sb.append(org.apache.commons.lang3.StringUtils.join(temp,"、"));
                        sb.append(";");
                    }
                    if (!CollectionUtils.isEmpty(newUserList)){
                        sb.append("新值：");
                        List<String> temp2 = new ArrayList<>();
                        for (BbsxpUsersVO bbsxpUsersVO : newUserList) {
                            String tempBuilder = bbsxpUsersVO.getMobile() + "(" +
                                    (StringUtils.isEmpty(bbsxpUsersVO.getCh999Name()) ? "会员:" + bbsxpUsersVO.getUserName() : "员工:" + bbsxpUsersVO.getCh999Name()) +
                                    ")";
                            temp2.add(tempBuilder);
                        }
                        sb.append(org.apache.commons.lang3.StringUtils.join(temp2,"、"));
                    }
                    if (!CollectionUtils.isEmpty(oldUserList) || !CollectionUtils.isEmpty(newUserList)){
                        comment = sb.toString();
                    }
                }
                sysConfigLog.setComment(comment);
                sysConfigLog.setInUserId(user.getUserId());
                sysConfigLog.setInUserName(user.getUserName());
                sysConfigLogList.add(sysConfigLog);
                editSysConfigList.add(newSysConfig);
                //删除缓存
                R<String> urlR = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL);
                if (ResultCode.SUCCESS == urlR.getCode() && StringUtils.isNotEmpty(urlR.getData())) {
                    String url = urlR.getData() + "/oa/clearcache.aspx?act=clearCache&key=sysConfigKeysV";
                    HttpUtil.get(url);
                }
            }
        }
    }

    /**
     * sysConfig.value 中文描述
     * @param config
     * @return
     */
    private String getSysConfigValueDesc(SysConfig config) {
        String value = Optional.ofNullable(config.getValue()).orElse("").trim();
        if (Objects.equals(config.getCode(), ConfigEnum.RECYCLE_FOLLOW_NEW_CULL_SUB_CONFIG.getCode()) && StringUtils.isNotEmpty(value)) {
            RecycleFollowNewSubConfigVO vo = JSON.parseObject(value, RecycleFollowNewSubConfigVO.class);
            return vo.getDesc();
        }
        return value;
    }

    /**
     * 删除配置项
     *
     * @param sysConfigList sysConfigList
     * @param list          list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Integer> removeSysConfig(List<SysConfig> sysConfigList, List<SysConfig> list, OaUserBO user) {
        List<Integer> remainList = sysConfigList.stream().map(SysConfig::getId).collect(Collectors.toList());
        //求交集
        remainList.retainAll(list.stream().map(SysConfig::getId).collect(Collectors.toList()));

        //求差集
        List<Integer> removeList = sysConfigList.stream().map(SysConfig::getId).collect(Collectors.toList());
        removeList.removeAll(remainList);

        if (CollectionUtils.isEmpty(removeList)) {
            return Collections.emptyList();
        }
        List<SysconfigLog> sysConfigLogList = new ArrayList<>(removeList.size());
        removeList.forEach(it -> removeSysConfigById(sysConfigList, sysConfigLogList, it, user));
        if (!CollectionUtils.isEmpty(sysConfigLogList)) {
            this.baseMapper.sqlServerBatchInsert(sysConfigLogList);
        }
        return removeList;
    }

    @Override
    public void sqlServerBatchInsert(List<SysconfigLog> sysConfigLogList) {
        this.baseMapper.sqlServerBatchInsert(sysConfigLogList);
    }

    @Override
    public List<SysconfigLog> selectLogsByCodes(List<Integer> list) {
        return this.baseMapper.selectLogsByCodes(list);
    }

    /**
     * removeSysConfigV2
     *
     * @param sysConfigList    sysConfigList
     * @param sysConfigLogList sysConfigLogList
     * @param id               id
     * @param user             user
     */
    private void removeSysConfigById(List<SysConfig> sysConfigList, List<SysconfigLog> sysConfigLogList, Integer id,
                                     OaUserBO user) {
        Optional<SysConfig> optSysConfig = sysConfigList.stream().filter(it -> it.getId().equals(id)).findFirst();
        if (optSysConfig.isPresent()) {
            SysConfig sysConfig = optSysConfig.get();
            SysconfigLog sysConfigLog = new SysconfigLog().setCode(sysConfig.getCode())
                    .setOperationType(OperationTypeEnum.SYSCONFIG_DELETE.getCode());
            sysConfigLog.setConfigId(sysConfig.getId());
            String comment = "删除配置：" + ConfigEnum.getMessage(sysConfig.getCode()) + "，原名称:" + sysConfig
                    .getName() + ",原值:" + sysConfig.getValue();
            sysConfigLog.setComment(comment);
            sysConfigLog.setInUserId(user.getUserId());
            sysConfigLog.setInUserName(user.getUserName());
            sysConfigLogList.add(sysConfigLog);
        }
    }
}

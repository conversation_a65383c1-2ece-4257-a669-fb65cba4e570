package com.jiuji.oa.afterservice.common.enums;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

@AllArgsConstructor
@Getter
public enum XtenantEnum implements CodeMessageEnumInterface {
    /**
     * 租户平台
     */

    Xtenant_JIUJI(0, "九机网"),
    Xtenant_YAYA(1, "丫丫网"),
    Xtenant_HUAWEI(2, "华为授权店"),
    Xtenant_APPLE(3, "九讯苹果体验店"),
    Xtenant_JDYD(4, "九电移动");


    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    /**九机与其他租户的分水岭*/
    public static final Integer JIUJI_SEPARATOR = 1000;

    /**
     * 是否为九机租户
     * @return
     */
    public static boolean isJiujiXtenant(){
        return isJiujiXtenant(getXtenant());
    }

    /**
     * 是否为输出租户
     * @return
     */
    public static boolean isSaasXtenant(){
        return !isJiujiXtenant();
    }

    /**
     * 是否未为九机租户
     * @return
     */
    public static boolean isJiujiXtenant(Long xtenant){
       return isJiujiXtenant(Convert.toInt(xtenant));
    }

    /**
     * 是否未为九机租户
     * @return
     */
    public static boolean isJiujiXtenant(Integer xtenant){
        if(xtenant == null){
            return false;
        }
        return xtenant<JIUJI_SEPARATOR;
    }

    /**
     * 获取当前主租户名称
     * @return
     */
    public static String getTenantName(){
        return Optional.ofNullable(System.getProperty("TenantId"))
                .orElseGet(() -> SpringContextUtil.getContext().getEnvironment().getProperty("instance-zone"));
    }

    /**
     * 获取当前用户所在的子租户编号
     * @return
     */
    public static Integer getXtenant(){
        return Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class)).map(AbstractCurrentRequestComponent::getCurrentStaffId)
                .map(OaUserBO::getXTenant).orElseGet(()->Convert.toInt(Namespaces.get()));
    }

    /**
     * 设置当前租户编号, mq或者多线程中使用
     * @return
     */
    public static void setXtenant(final long namespace){
        Namespaces.set(namespace);
    }
}
